#!/usr/bin/env python3
"""
Test Smart Y-Component Optimizer
Validate the smart Y-component optimization that handles ascent/descent trajectories
"""

import numpy as np
import pandas as pd
import time
import json
from pathlib import Path

# Import our modules
from data_loader import ELOPEDataLoader
from smart_y_optimizer import SmartYComponentOptimizer

def test_smart_y_optimizer():
    """
    Test the smart Y-component optimizer across multiple sequences
    """
    print("🚀 Smart Y-Component Optimizer Test")
    print("=" * 60)
    
    # Initialize components
    data_loader = ELOPEDataLoader("..")
    smart_optimizer = SmartYComponentOptimizer()
    
    # Test sequences
    test_sequences = [0, 1, 2, 3, 4]
    
    results = {
        'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'sequences_tested': test_sequences,
        'sequence_results': [],
        'overall_stats': {}
    }
    
    all_baseline_errors = []
    all_smart_errors = []
    all_improvements = []
    all_processing_times = []
    
    for seq_id in test_sequences:
        print(f"\n📊 Testing Sequence {seq_id}")
        print("-" * 30)
        
        # Load sequence data
        seq_data = data_loader.load_sequence(seq_id)
        if seq_data is None:
            print(f"  ❌ Failed to load sequence {seq_id}")
            continue
        
        # Extract data
        events_df = pd.DataFrame(seq_data['events'])
        events_df['t'] = events_df['t'] / 1e6  # Convert to seconds
        trajectory = seq_data['trajectory']
        timestamps = seq_data['timestamps']
        range_data = seq_data.get('range_meter', None)
        
        print(f"  Events: {len(events_df):,}")
        print(f"  Timestamps: {len(timestamps)}")
        print(f"  Range data: {len(range_data) if range_data is not None else 0}")
        
        # Analyze trajectory type
        trajectory_type = smart_optimizer.analyze_trajectory_type(trajectory, seq_id)
        
        # Test on selected timestamps
        test_indices = list(range(10, len(timestamps), 10))[:10]  # Max 10 tests per sequence
        
        seq_baseline_errors = []
        seq_smart_errors = []
        seq_improvements = []
        seq_processing_times = []
        
        for i in test_indices:
            timestamp = timestamps[i]
            gt_velocity = trajectory[i, 4]  # Ground truth Y velocity
            
            # Extract events around timestamp
            time_window = 0.1  # 100ms
            start_time = timestamp - time_window / 2
            end_time = timestamp + time_window / 2
            
            window_events = events_df[
                (events_df['t'] >= start_time) & (events_df['t'] <= end_time)
            ]
            
            if len(window_events) < 100:
                continue
            
            # Baseline: simple range-based estimation
            baseline_velocity = 0.0
            if range_data is not None and len(range_data) >= 3:
                time_diffs = np.abs(range_data[:, 0] - timestamp)
                center_idx = np.argmin(time_diffs)
                
                start_idx = max(0, center_idx - 1)
                end_idx = min(len(range_data), center_idx + 2)
                
                if end_idx - start_idx >= 3:
                    local_data = range_data[start_idx:end_idx]
                    times = local_data[:, 0]
                    ranges = local_data[:, 1]
                    
                    try:
                        coeffs = np.polyfit(times, ranges, 2)
                        velocity_at_timestamp = 2 * coeffs[0] * timestamp + coeffs[1]
                        baseline_velocity = -velocity_at_timestamp
                    except:
                        if len(times) >= 2:
                            dt = times[-1] - times[0]
                            dr = ranges[-1] - ranges[0]
                            if dt > 0:
                                baseline_velocity = -dr / dt
            
            # Smart optimization
            try:
                opt_start = time.time()
                smart_velocity, confidence = smart_optimizer.estimate_y_velocity(
                    window_events, range_data, trajectory, timestamp, None, None, seq_id
                )
                opt_time = time.time() - opt_start
                
                # Calculate errors
                baseline_error = abs(gt_velocity - baseline_velocity)
                smart_error = abs(gt_velocity - smart_velocity)
                
                # Calculate improvement
                if baseline_error > 0:
                    improvement = (baseline_error - smart_error) / baseline_error * 100
                else:
                    improvement = 0
                
                seq_baseline_errors.append(baseline_error)
                seq_smart_errors.append(smart_error)
                seq_improvements.append(improvement)
                seq_processing_times.append(opt_time)
                
                # Debug output for first few tests
                if len(seq_baseline_errors) <= 3:
                    print(f"    Test {len(seq_baseline_errors)}: GT={gt_velocity:.1f}, Baseline={baseline_velocity:.1f}, Smart={smart_velocity:.1f}, Improvement={improvement:.1f}%")
                
            except Exception as e:
                print(f"    ⚠️  Smart optimization failed at timestamp {i}: {e}")
        
        # Sequence summary
        if seq_baseline_errors:
            avg_baseline_error = np.mean(seq_baseline_errors)
            avg_smart_error = np.mean(seq_smart_errors)
            avg_improvement = np.mean(seq_improvements)
            avg_processing_time = np.mean(seq_processing_times)
            
            print(f"  📈 Results ({len(seq_baseline_errors)} tests):")
            print(f"    Baseline error: {avg_baseline_error:.2f} m/s")
            print(f"    Smart error: {avg_smart_error:.2f} m/s")
            print(f"    Improvement: {avg_improvement:.1f}%")
            print(f"    Avg processing time: {avg_processing_time:.3f}s")
            print(f"    Trajectory type: {trajectory_type}")
            
            # Store sequence results
            seq_result = {
                'sequence_id': seq_id,
                'trajectory_type': trajectory_type,
                'tests_performed': len(seq_baseline_errors),
                'average_baseline_error': avg_baseline_error,
                'average_smart_error': avg_smart_error,
                'average_improvement': avg_improvement,
                'average_processing_time': avg_processing_time,
                'baseline_errors': seq_baseline_errors,
                'smart_errors': seq_smart_errors,
                'improvements': seq_improvements
            }
            
            results['sequence_results'].append(seq_result)
            
            # Add to overall stats
            all_baseline_errors.extend(seq_baseline_errors)
            all_smart_errors.extend(seq_smart_errors)
            all_improvements.extend(seq_improvements)
            all_processing_times.extend(seq_processing_times)
            
            if avg_improvement > 30:
                print(f"    🎉 EXCELLENT improvement!")
            elif avg_improvement > 15:
                print(f"    ✅ GOOD improvement!")
            elif avg_improvement > 0:
                print(f"    👍 MODERATE improvement!")
            else:
                print(f"    ❌ NEEDS WORK!")
        else:
            print(f"  ❌ No valid tests performed")
    
    # Overall results
    if all_baseline_errors:
        overall_baseline_error = np.mean(all_baseline_errors)
        overall_smart_error = np.mean(all_smart_errors)
        overall_improvement = np.mean(all_improvements)
        overall_processing_time = np.mean(all_processing_times)
        
        print(f"\n🎯 Overall Results")
        print("=" * 40)
        print(f"Total tests: {len(all_baseline_errors)}")
        print(f"Sequences tested: {len(results['sequence_results'])}")
        print(f"Overall baseline error: {overall_baseline_error:.2f} m/s")
        print(f"Overall smart error: {overall_smart_error:.2f} m/s")
        print(f"Overall improvement: {overall_improvement:.1f}%")
        print(f"Average processing time: {overall_processing_time:.3f}s")
        
        # Store overall stats
        results['overall_stats'] = {
            'total_tests': len(all_baseline_errors),
            'sequences_tested': len(results['sequence_results']),
            'overall_baseline_error': overall_baseline_error,
            'overall_smart_error': overall_smart_error,
            'overall_improvement': overall_improvement,
            'average_processing_time': overall_processing_time,
            'baseline_error_std': np.std(all_baseline_errors),
            'smart_error_std': np.std(all_smart_errors),
            'improvement_std': np.std(all_improvements)
        }
        
        # Assessment
        print(f"\n📊 Assessment:")
        if overall_improvement >= 50:
            print(f"🎉 OUTSTANDING: {overall_improvement:.1f}% improvement! Far exceeds target!")
            assessment = "OUTSTANDING"
        elif overall_improvement >= 30:
            print(f"🎉 EXCELLENT: {overall_improvement:.1f}% improvement! Target exceeded!")
            assessment = "EXCELLENT"
        elif overall_improvement >= 20:
            print(f"✅ VERY GOOD: {overall_improvement:.1f}% improvement!")
            assessment = "VERY_GOOD"
        elif overall_improvement >= 10:
            print(f"👍 GOOD: {overall_improvement:.1f}% improvement!")
            assessment = "GOOD"
        elif overall_improvement > 0:
            print(f"📈 MODERATE: {overall_improvement:.1f}% improvement!")
            assessment = "MODERATE"
        else:
            print(f"❌ NEEDS WORK: {overall_improvement:.1f}% - optimization needs improvement")
            assessment = "NEEDS_WORK"
        
        results['overall_stats']['assessment'] = assessment
        
        # Smart optimizer performance
        smart_perf = smart_optimizer.get_performance_summary()
        results['smart_optimizer_performance'] = smart_perf
        
        print(f"\n🔧 Smart Optimizer Performance:")
        for method in ['range_based', 'optical_flow', 'physics_model', 'interpolation']:
            success_rate = smart_perf.get(f'{method}_success_rate', 0.0)
            print(f"  {method} success rate: {success_rate:.1%}")
        
        print(f"  Average confidence: {smart_perf.get('avg_confidence', 0.0):.3f}")
        print(f"  Trajectory type: {smart_perf.get('trajectory_type', 'unknown')}")
        print(f"  Velocity direction: {smart_perf.get('velocity_direction', 1.0)}")
        
        # Trajectory type breakdown
        print(f"\n📊 Trajectory Type Breakdown:")
        trajectory_types = {}
        for seq_result in results['sequence_results']:
            traj_type = seq_result['trajectory_type']
            if traj_type not in trajectory_types:
                trajectory_types[traj_type] = {'count': 0, 'avg_improvement': 0.0, 'improvements': []}
            trajectory_types[traj_type]['count'] += 1
            trajectory_types[traj_type]['improvements'].append(seq_result['average_improvement'])
        
        for traj_type, data in trajectory_types.items():
            avg_improvement = np.mean(data['improvements'])
            print(f"  {traj_type}: {data['count']} sequences, {avg_improvement:.1f}% avg improvement")
        
        # Save results
        results_file = Path("smart_y_optimizer_results.json")
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to {results_file}")
        
        return results
    else:
        print(f"\n❌ No valid results obtained")
        return None

def main():
    """
    Main test function
    """
    print("🚀 Smart Y-Component Optimizer Test")
    print("Testing intelligent Y-component optimization with trajectory awareness")
    print("=" * 80)
    
    results = test_smart_y_optimizer()
    
    if results:
        overall_improvement = results['overall_stats']['overall_improvement']
        assessment = results['overall_stats']['assessment']
        
        print(f"\n✅ Smart Y-component optimizer test completed")
        print(f"🎯 Overall improvement: {overall_improvement:.1f}%")
        print(f"📊 Assessment: {assessment}")
        
        if overall_improvement >= 20:
            print(f"🎉 Smart optimization successful! Ready for Phase 2!")
        else:
            print(f"🔧 Smart optimization needs further refinement")
    else:
        print(f"\n❌ Smart Y-component optimizer test failed")

if __name__ == "__main__":
    main()
