#!/usr/bin/env python3
"""
Phase 2 Performance Validation
Comprehensive testing of Phase 2 enhanced solver on real ELOPE data
"""

import numpy as np
import pandas as pd
import time
import json
import os
from typing import Dict, List, Tuple, Optional
from phase2_enhanced_solver import Phase2EnhancedSolver
from smart_y_optimizer import SmartYComponentOptimizer

class Phase2Validator:
    """Comprehensive Phase 2 validation system"""
    
    def __init__(self):
        self.phase1_solver = SmartYComponentOptimizer()
        self.phase2_solver = Phase2EnhancedSolver()
        self.results = {}
        
    def load_sequence_data(self, sequence_id: int) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """Load sequence data from NPZ file"""
        try:
            data_file = f"../train/{sequence_id:04d}.npz"
            if not os.path.exists(data_file):
                print(f"Data file not found: {data_file}")
                return None, None
            
            data = np.load(data_file)
            
            # Extract events
            events = data['events']
            events_df = pd.DataFrame({
                'x': events['x'],
                'y': events['y'],
                'p': events['p'],
                't': events['t']
            })
            
            # Extract ground truth trajectory
            gt_df = None
            if 'traj' in data and 'timestamps' in data:
                traj = data['traj']
                timestamps = data['timestamps']
                
                gt_df = pd.DataFrame({
                    't': timestamps,
                    'x': traj[:, 0],
                    'y': traj[:, 1], 
                    'z': traj[:, 2],
                    'vx': traj[:, 7] if traj.shape[1] > 7 else np.zeros(len(timestamps)),
                    'vy': traj[:, 8] if traj.shape[1] > 8 else np.zeros(len(timestamps)),
                    'vz': traj[:, 9] if traj.shape[1] > 9 else np.zeros(len(timestamps))
                })
            
            return events_df, gt_df
            
        except Exception as e:
            print(f"Error loading sequence {sequence_id}: {e}")
            return None, None
    
    def analyze_trajectory_type(self, gt_df: pd.DataFrame) -> str:
        """Analyze trajectory type from ground truth"""
        if gt_df is None or len(gt_df) == 0:
            return 'unknown'
        
        velocities = gt_df['vy'].values
        mean_vy = np.mean(velocities)
        std_vy = np.std(velocities)
        
        if abs(mean_vy) < std_vy * 0.3:
            return 'hovering'
        elif mean_vy < -5:
            return 'ascent'
        elif mean_vy > 5:
            return 'descent'
        else:
            return 'mixed'
    
    def validate_sequence(self, sequence_id: int, num_test_points: int = 50) -> Dict:
        """Validate Phase 2 performance on a single sequence"""
        print(f"\n=== Validating Sequence {sequence_id:04d} ===")
        
        # Load data
        events_df, gt_df = self.load_sequence_data(sequence_id)
        if events_df is None:
            return {'sequence_id': sequence_id, 'status': 'failed', 'error': 'data_load_failed'}
        
        # Analyze sequence characteristics
        trajectory_type = self.analyze_trajectory_type(gt_df)
        event_count = len(events_df)
        
        # Convert timestamps to seconds
        event_timestamps = events_df['t'].values.astype(float)
        if np.max(event_timestamps) > 1e6:
            event_timestamps = event_timestamps / 1e6
            events_df['t'] = event_timestamps
        
        # Adapt solver parameters
        sequence_chars = {
            'event_density': event_count / (np.max(event_timestamps) - np.min(event_timestamps)),
            'trajectory_type': trajectory_type,
            'event_count': event_count
        }
        
        self.phase2_solver.adapt_optical_flow_parameters(sequence_chars)
        
        print(f"  Trajectory Type: {trajectory_type}")
        print(f"  Event Count: {event_count:,}")
        print(f"  Event Density: {sequence_chars['event_density']:.0f} events/sec")
        
        # Generate test timestamps
        time_span = np.max(event_timestamps) - np.min(event_timestamps)
        test_timestamps = np.linspace(
            np.min(event_timestamps) + time_span * 0.1,
            np.max(event_timestamps) - time_span * 0.1,
            num_test_points
        )
        
        # Test both phases
        phase1_results = []
        phase2_results = []
        phase1_times = []
        phase2_times = []
        optical_flow_successes = 0
        
        print(f"  Testing {len(test_timestamps)} time points...")
        
        for i, timestamp in enumerate(test_timestamps):
            if i % 10 == 0:
                print(f"    Progress: {i}/{len(test_timestamps)}")
            
            # Create time window for events
            window_size = 0.05  # 50ms window
            event_mask = (events_df['t'] >= timestamp - window_size) & (events_df['t'] <= timestamp + window_size)
            window_events = events_df[event_mask]
            
            if len(window_events) < 50:  # Need minimum events
                continue
            
            # Create dummy range and trajectory data
            range_data = np.array([[timestamp, 100.0]])
            trajectory = np.array([[0, 0, 100, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
            
            # Test Phase 1
            start_time = time.time()
            try:
                phase1_vel, phase1_conf = self.phase1_solver.estimate_y_velocity(
                    window_events, range_data, trajectory, timestamp
                )
                phase1_time = time.time() - start_time
                phase1_results.append((phase1_vel, phase1_conf))
                phase1_times.append(phase1_time)
            except Exception as e:
                print(f"    Phase 1 failed at {timestamp}: {e}")
                continue
            
            # Test Phase 2
            start_time = time.time()
            try:
                phase2_vel, phase2_conf = self.phase2_solver.estimate_y_velocity_enhanced(
                    window_events, timestamp
                )
                phase2_time = time.time() - start_time
                phase2_results.append((phase2_vel, phase2_conf))
                phase2_times.append(phase2_time)
                
                # Check if optical flow contributed
                optical_vel, optical_conf, optical_success = self.phase2_solver.estimate_from_optical_flow_enhanced(
                    window_events, timestamp
                )
                if optical_success:
                    optical_flow_successes += 1
                    
            except Exception as e:
                print(f"    Phase 2 failed at {timestamp}: {e}")
                phase2_results.append(phase1_results[-1])  # Fallback to Phase 1
                phase2_times.append(phase1_times[-1])
        
        if len(phase1_results) == 0:
            return {'sequence_id': sequence_id, 'status': 'failed', 'error': 'no_valid_results'}
        
        # Calculate performance metrics
        phase1_velocities = [r[0] for r in phase1_results]
        phase1_confidences = [r[1] for r in phase1_results]
        phase2_velocities = [r[0] for r in phase2_results]
        phase2_confidences = [r[1] for r in phase2_results]
        
        results = {
            'sequence_id': sequence_id,
            'status': 'success',
            'trajectory_type': trajectory_type,
            'event_count': event_count,
            'event_density': sequence_chars['event_density'],
            'test_points': len(phase1_results),
            
            # Phase 1 metrics
            'phase1_mean_velocity': np.mean(phase1_velocities),
            'phase1_std_velocity': np.std(phase1_velocities),
            'phase1_mean_confidence': np.mean(phase1_confidences),
            'phase1_avg_time': np.mean(phase1_times),
            
            # Phase 2 metrics
            'phase2_mean_velocity': np.mean(phase2_velocities),
            'phase2_std_velocity': np.std(phase2_velocities),
            'phase2_mean_confidence': np.mean(phase2_confidences),
            'phase2_avg_time': np.mean(phase2_times),
            
            # Optical flow metrics
            'optical_flow_success_rate': optical_flow_successes / len(phase2_results),
            
            # Improvements
            'velocity_difference': np.mean(phase2_velocities) - np.mean(phase1_velocities),
            'confidence_improvement': np.mean(phase2_confidences) - np.mean(phase1_confidences),
            'time_overhead': np.mean(phase2_times) - np.mean(phase1_times),
            'time_overhead_percent': ((np.mean(phase2_times) - np.mean(phase1_times)) / np.mean(phase1_times)) * 100
        }
        
        # Calculate RMSE if ground truth available
        if gt_df is not None and len(gt_df) > 0:
            try:
                # Interpolate ground truth to test timestamps
                gt_timestamps = gt_df['t'].values
                gt_velocities = gt_df['vy'].values
                
                # Simple interpolation
                from scipy.interpolate import interp1d
                if len(gt_timestamps) > 1:
                    interp_func = interp1d(gt_timestamps, gt_velocities, 
                                         bounds_error=False, fill_value='extrapolate')
                    
                    # Use subset of test timestamps that have results
                    valid_test_timestamps = test_timestamps[:len(phase1_results)]
                    interpolated_gt = interp_func(valid_test_timestamps)
                    
                    # Calculate RMSE
                    phase1_rmse = np.sqrt(np.mean((np.array(phase1_velocities) - interpolated_gt) ** 2))
                    phase2_rmse = np.sqrt(np.mean((np.array(phase2_velocities) - interpolated_gt) ** 2))
                    
                    results['phase1_rmse'] = phase1_rmse
                    results['phase2_rmse'] = phase2_rmse
                    results['rmse_improvement'] = phase1_rmse - phase2_rmse
                    results['rmse_improvement_percent'] = ((phase1_rmse - phase2_rmse) / phase1_rmse) * 100
                    
            except Exception as e:
                print(f"    RMSE calculation failed: {e}")
        
        # Get performance summaries
        phase1_perf = self.phase1_solver.get_performance_summary()
        phase2_perf = self.phase2_solver.get_phase2_performance_summary()
        
        results['phase1_performance'] = phase1_perf
        results['phase2_performance'] = phase2_perf
        
        return results
    
    def run_comprehensive_validation(self, sequences: List[int] = None) -> Dict:
        """Run comprehensive validation on multiple sequences"""
        if sequences is None:
            sequences = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]  # First 10 sequences
        
        print("=== Phase 2 Comprehensive Validation ===")
        print(f"Testing sequences: {sequences}")
        
        all_results = []
        
        for seq_id in sequences:
            try:
                result = self.validate_sequence(seq_id)
                all_results.append(result)
                
                if result['status'] == 'success':
                    print(f"\n  Sequence {seq_id:04d} Summary:")
                    print(f"    Trajectory: {result['trajectory_type']}")
                    print(f"    Test Points: {result['test_points']}")
                    print(f"    Phase 1 Velocity: {result['phase1_mean_velocity']:.2f} ± {result['phase1_std_velocity']:.2f}")
                    print(f"    Phase 2 Velocity: {result['phase2_mean_velocity']:.2f} ± {result['phase2_std_velocity']:.2f}")
                    print(f"    Confidence Improvement: {result['confidence_improvement']:.3f}")
                    print(f"    Optical Flow Success: {result['optical_flow_success_rate']:.1%}")
                    print(f"    Time Overhead: {result['time_overhead_percent']:.1f}%")
                    
                    if 'rmse_improvement_percent' in result:
                        print(f"    RMSE Improvement: {result['rmse_improvement_percent']:.1f}%")
                
            except Exception as e:
                print(f"Error validating sequence {seq_id}: {e}")
                all_results.append({'sequence_id': seq_id, 'status': 'error', 'error': str(e)})
        
        # Calculate overall statistics
        successful_results = [r for r in all_results if r['status'] == 'success']
        
        if successful_results:
            summary = self.calculate_validation_summary(successful_results)
            
            print(f"\n=== Overall Phase 2 Validation Summary ===")
            print(f"Successful validations: {len(successful_results)}/{len(sequences)}")
            print(f"Average confidence improvement: {summary['avg_confidence_improvement']:.3f}")
            print(f"Average optical flow success rate: {summary['avg_optical_flow_success_rate']:.1%}")
            print(f"Average time overhead: {summary['avg_time_overhead_percent']:.1f}%")
            
            if 'avg_rmse_improvement_percent' in summary:
                print(f"Average RMSE improvement: {summary['avg_rmse_improvement_percent']:.1f}%")
            
            # Trajectory type breakdown
            for traj_type in summary['trajectory_breakdown']:
                breakdown = summary['trajectory_breakdown'][traj_type]
                print(f"\n{traj_type.capitalize()} trajectories ({breakdown['count']} sequences):")
                print(f"  Confidence improvement: {breakdown['avg_confidence_improvement']:.3f}")
                print(f"  Optical flow success: {breakdown['avg_optical_flow_success_rate']:.1%}")
                if 'avg_rmse_improvement_percent' in breakdown:
                    print(f"  RMSE improvement: {breakdown['avg_rmse_improvement_percent']:.1f}%")
        
        # Save results
        validation_results = {
            'validation_timestamp': time.time(),
            'sequences_tested': sequences,
            'individual_results': all_results,
            'summary': summary if successful_results else None
        }
        
        with open('phase2_validation_results.json', 'w') as f:
            json.dump(validation_results, f, indent=2, default=str)
        
        print(f"\nValidation results saved to: phase2_validation_results.json")
        
        return validation_results
    
    def calculate_validation_summary(self, results: List[Dict]) -> Dict:
        """Calculate summary statistics from validation results"""
        summary = {
            'total_sequences': len(results),
            'avg_confidence_improvement': np.mean([r['confidence_improvement'] for r in results]),
            'avg_optical_flow_success_rate': np.mean([r['optical_flow_success_rate'] for r in results]),
            'avg_time_overhead_percent': np.mean([r['time_overhead_percent'] for r in results]),
        }
        
        # RMSE improvements (if available)
        rmse_improvements = [r.get('rmse_improvement_percent', 0) for r in results 
                           if 'rmse_improvement_percent' in r]
        if rmse_improvements:
            summary['avg_rmse_improvement_percent'] = np.mean(rmse_improvements)
        
        # Trajectory type breakdown
        trajectory_breakdown = {}
        for traj_type in ['ascent', 'descent', 'hovering', 'mixed', 'unknown']:
            traj_results = [r for r in results if r['trajectory_type'] == traj_type]
            if traj_results:
                breakdown = {
                    'count': len(traj_results),
                    'avg_confidence_improvement': np.mean([r['confidence_improvement'] for r in traj_results]),
                    'avg_optical_flow_success_rate': np.mean([r['optical_flow_success_rate'] for r in traj_results]),
                }
                
                traj_rmse_improvements = [r.get('rmse_improvement_percent', 0) for r in traj_results 
                                        if 'rmse_improvement_percent' in r]
                if traj_rmse_improvements:
                    breakdown['avg_rmse_improvement_percent'] = np.mean(traj_rmse_improvements)
                
                trajectory_breakdown[traj_type] = breakdown
        
        summary['trajectory_breakdown'] = trajectory_breakdown
        
        return summary

def main():
    """Main validation function"""
    validator = Phase2Validator()
    
    # Run validation on first 10 sequences
    results = validator.run_comprehensive_validation([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])
    
    return results

if __name__ == "__main__":
    results = main()
