#!/usr/bin/env python3
"""
Adaptive Feature Extractor with dynamic time window and enhanced event processing.
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any, Optional
import cv2
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

class AdaptiveTimeWindowStrategy:
    """
    Adaptive time window strategy that adjusts based on event density and motion characteristics.
    """
    
    def __init__(self, base_window: float = 1000000, min_window: float = 200000, max_window: float = 2000000):
        """
        Initialize adaptive time window strategy.
        
        Args:
            base_window: Base time window in microseconds
            min_window: Minimum allowed window size
            max_window: Maximum allowed window size
        """
        self.base_window = base_window
        self.min_window = min_window
        self.max_window = max_window
        
        # Adaptation parameters
        self.target_events_per_frame = 5000  # Target number of events per frame
        self.min_events_per_frame = 1000     # Minimum events for valid frame
        self.max_events_per_frame = 20000    # Maximum events to avoid overload
        
    def calculate_adaptive_window(self, events_df: pd.DataFrame, 
                                 start_time: float,
                                 motion_velocity: float = 0.0) -> float:
        """
        Calculate adaptive time window based on event density and motion.
        
        Args:
            events_df: Event data
            start_time: Starting time for window calculation
            motion_velocity: Estimated motion velocity
            
        Returns:
            Adaptive window size in microseconds
        """
        # Calculate local event density
        sample_window = self.base_window * 0.5  # Sample window for density estimation
        sample_end = start_time + sample_window
        
        sample_events = events_df[
            (events_df['t'] >= start_time) & 
            (events_df['t'] < sample_end)
        ]
        
        if len(sample_events) == 0:
            return self.base_window
        
        # Calculate event rate (events per microsecond)
        event_rate = len(sample_events) / sample_window
        
        # Estimate required window for target events
        if event_rate > 0:
            target_window = self.target_events_per_frame / event_rate
        else:
            target_window = self.base_window
        
        # Adjust based on motion velocity
        motion_factor = self._calculate_motion_factor(motion_velocity)
        adapted_window = target_window * motion_factor
        
        # Apply constraints
        adapted_window = max(self.min_window, min(adapted_window, self.max_window))
        
        return adapted_window
    
    def _calculate_motion_factor(self, velocity: float) -> float:
        """
        Calculate motion-based adaptation factor.
        Higher velocity -> smaller windows for better temporal resolution.
        """
        # Normalize velocity (assuming typical range 0-200 m/s)
        normalized_velocity = min(velocity / 200.0, 1.0)
        
        # Motion factor: 0.5 to 1.5 range
        motion_factor = 1.5 - normalized_velocity
        
        return max(0.5, min(motion_factor, 1.5))

class EnhancedEventFrameGenerator:
    """
    Enhanced event frame generator with adaptive windows and multiple representation methods.
    """
    
    def __init__(self, width: int = 200, height: int = 200):
        """Initialize enhanced event frame generator."""
        self.width = width
        self.height = height
        self.adaptive_strategy = AdaptiveTimeWindowStrategy()
        
    def generate_adaptive_frame_sequence(self, events_df: pd.DataFrame,
                                       trajectory_data: np.ndarray = None,
                                       overlap: float = 0.2) -> List[Tuple[np.ndarray, float]]:
        """
        Generate frame sequence with adaptive time windows.
        
        Args:
            events_df: Event data
            trajectory_data: Optional trajectory data for motion estimation
            overlap: Overlap between consecutive frames
            
        Returns:
            List of (frame, timestamp) tuples
        """
        if len(events_df) == 0:
            return []
        
        frames = []
        start_time = events_df['t'].min()
        end_time = events_df['t'].max()
        
        current_time = start_time
        frame_count = 0
        
        while current_time < end_time:
            # Estimate current motion velocity
            motion_velocity = self._estimate_motion_velocity(
                trajectory_data, current_time, events_df
            ) if trajectory_data is not None else 0.0
            
            # Calculate adaptive window
            window_size = self.adaptive_strategy.calculate_adaptive_window(
                events_df, current_time, motion_velocity
            )
            
            # Generate frame with multiple methods
            frame = self._generate_enhanced_frame(
                events_df, current_time, window_size
            )
            
            if frame is not None:
                frame_timestamp = current_time + window_size / 2
                frames.append((frame, frame_timestamp))
            
            # Move to next window with overlap
            current_time += window_size * (1 - overlap)
            frame_count += 1
            
            # Safety check to prevent infinite loops
            if frame_count > 1000:
                break
        
        return frames
    
    def _estimate_motion_velocity(self, trajectory_data: np.ndarray, 
                                 current_time: float, events_df: pd.DataFrame) -> float:
        """
        Estimate motion velocity at current time.
        """
        try:
            # Convert event time to seconds for trajectory matching
            current_time_sec = current_time / 1e6
            
            # Find closest trajectory point
            if len(trajectory_data) > 0:
                # Assuming trajectory has timestamps in first column or use index
                velocities = trajectory_data[:, 3:6]  # vx, vy, vz
                velocity_magnitudes = np.linalg.norm(velocities, axis=1)
                
                # Use average velocity as approximation
                return np.mean(velocity_magnitudes)
            
        except Exception:
            pass
        
        return 0.0
    
    def _generate_enhanced_frame(self, events_df: pd.DataFrame, 
                               start_time: float, window_size: float) -> Optional[np.ndarray]:
        """
        Generate enhanced event frame using multiple representation methods.
        """
        end_time = start_time + window_size
        
        # Filter events in time window
        window_events = events_df[
            (events_df['t'] >= start_time) & 
            (events_df['t'] < end_time)
        ]
        
        if len(window_events) < self.adaptive_strategy.min_events_per_frame:
            return None
        
        # Generate multiple representations and combine
        frame_latest = self._generate_latest_event_frame(window_events)
        frame_count = self._generate_event_count_frame(window_events)
        frame_time_surface = self._generate_time_surface_frame(window_events, start_time, window_size)
        
        # Combine representations
        combined_frame = self._combine_representations(
            frame_latest, frame_count, frame_time_surface
        )
        
        return combined_frame
    
    def _generate_latest_event_frame(self, events: pd.DataFrame) -> np.ndarray:
        """Generate frame showing latest events at each pixel."""
        frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
        
        if len(events) == 0:
            return frame
        
        # Get latest event at each pixel
        latest_events = events.loc[events.groupby(['x', 'y'])['t'].idxmax()]
        
        # Separate by polarity
        pos_events = latest_events[latest_events['p'] == True]
        neg_events = latest_events[latest_events['p'] == False]
        
        # Set pixels
        if len(pos_events) > 0:
            frame[pos_events['y'], pos_events['x'], 0] = 255  # Red for positive
        
        if len(neg_events) > 0:
            frame[neg_events['y'], neg_events['x'], 2] = 255  # Blue for negative
        
        return frame
    
    def _generate_event_count_frame(self, events: pd.DataFrame) -> np.ndarray:
        """Generate frame based on event counts."""
        frame = np.zeros((self.height, self.width), dtype=np.float32)
        
        if len(events) == 0:
            return frame
        
        # Count events at each pixel
        event_counts = events.groupby(['x', 'y']).size().reset_index(name='count')
        
        # Normalize counts
        max_count = event_counts['count'].max() if len(event_counts) > 0 else 1
        normalized_counts = event_counts['count'] / max_count
        
        # Set frame values
        frame[event_counts['y'], event_counts['x']] = normalized_counts
        
        return frame
    
    def _generate_time_surface_frame(self, events: pd.DataFrame, 
                                   start_time: float, window_size: float) -> np.ndarray:
        """Generate time surface representation."""
        frame = np.zeros((self.height, self.width), dtype=np.float32)
        
        if len(events) == 0:
            return frame
        
        # Normalize timestamps within window
        normalized_times = (events['t'] - start_time) / window_size
        
        # Create time surface (latest timestamp at each pixel)
        for _, event in events.iterrows():
            x, y = int(event['x']), int(event['y'])
            if 0 <= x < self.width and 0 <= y < self.height:
                frame[y, x] = max(frame[y, x], normalized_times.loc[event.name])
        
        return frame
    
    def _combine_representations(self, frame_latest: np.ndarray, 
                               frame_count: np.ndarray, 
                               frame_time_surface: np.ndarray) -> np.ndarray:
        """
        Combine multiple event representations into a single enhanced frame.
        """
        # Convert latest event frame to grayscale
        if len(frame_latest.shape) == 3:
            latest_gray = cv2.cvtColor(frame_latest, cv2.COLOR_BGR2GRAY)
        else:
            latest_gray = frame_latest
        
        # Normalize all frames to [0, 1]
        latest_norm = latest_gray.astype(np.float32) / 255.0
        count_norm = frame_count / (np.max(frame_count) + 1e-10)
        time_norm = frame_time_surface
        
        # Weighted combination
        combined = (
            latest_norm * 0.5 +      # Latest events (structure)
            count_norm * 0.3 +       # Event density (activity)
            time_norm * 0.2          # Temporal information
        )
        
        # Convert back to uint8
        combined_frame = (combined * 255).astype(np.uint8)
        
        # Apply enhancement filters
        enhanced_frame = self._enhance_frame(combined_frame)
        
        return enhanced_frame
    
    def _enhance_frame(self, frame: np.ndarray) -> np.ndarray:
        """Apply enhancement filters to improve frame quality."""
        
        # Apply Gaussian smoothing to reduce noise
        smoothed = cv2.GaussianBlur(frame, (3, 3), 0.5)
        
        # Apply contrast enhancement
        enhanced = cv2.convertScaleAbs(smoothed, alpha=1.2, beta=10)
        
        # Apply sharpening filter
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # Blend original and sharpened
        final_frame = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
        
        return final_frame

class MotionConstrainedFrameGenerator(EnhancedEventFrameGenerator):
    """
    Frame generator with motion constraints and temporal consistency.
    """
    
    def __init__(self, width: int = 200, height: int = 200):
        """Initialize motion-constrained frame generator."""
        super().__init__(width, height)
        self.previous_frame = None
        self.temporal_consistency_weight = 0.3
        
    def generate_motion_aware_sequence(self, events_df: pd.DataFrame,
                                     trajectory_data: np.ndarray,
                                     timestamps: np.ndarray) -> List[Tuple[np.ndarray, float]]:
        """
        Generate frame sequence with motion awareness and temporal consistency.
        """
        frames = []
        
        # Reset previous frame
        self.previous_frame = None
        
        # Generate adaptive frames
        adaptive_frames = self.generate_adaptive_frame_sequence(
            events_df, trajectory_data, overlap=0.3
        )
        
        for i, (frame, timestamp) in enumerate(adaptive_frames):
            # Apply temporal consistency
            if self.previous_frame is not None:
                frame = self._apply_temporal_consistency(frame, self.previous_frame)
            
            # Apply motion-based filtering
            motion_filtered_frame = self._apply_motion_filtering(
                frame, trajectory_data, timestamp, timestamps
            )
            
            frames.append((motion_filtered_frame, timestamp))
            self.previous_frame = motion_filtered_frame
        
        return frames
    
    def _apply_temporal_consistency(self, current_frame: np.ndarray, 
                                  previous_frame: np.ndarray) -> np.ndarray:
        """Apply temporal consistency between consecutive frames."""
        
        # Ensure same dimensions
        if current_frame.shape != previous_frame.shape:
            return current_frame
        
        # Weighted blend for temporal smoothing
        consistent_frame = cv2.addWeighted(
            current_frame, 1 - self.temporal_consistency_weight,
            previous_frame, self.temporal_consistency_weight,
            0
        )
        
        return consistent_frame
    
    def _apply_motion_filtering(self, frame: np.ndarray, 
                              trajectory_data: np.ndarray,
                              frame_timestamp: float,
                              timestamps: np.ndarray) -> np.ndarray:
        """Apply motion-based filtering to enhance relevant features."""
        
        try:
            # Find closest trajectory point
            frame_time_sec = frame_timestamp / 1e6
            
            if len(timestamps) > 0:
                closest_idx = np.argmin(np.abs(timestamps - frame_time_sec))
                
                if closest_idx < len(trajectory_data):
                    # Get velocity at this time
                    velocity = trajectory_data[closest_idx, 3:6]  # vx, vy, vz
                    velocity_magnitude = np.linalg.norm(velocity)
                    
                    # Apply motion-based enhancement
                    if velocity_magnitude > 10.0:  # High motion
                        # Enhance edges for better feature tracking
                        enhanced_frame = self._enhance_edges(frame)
                    else:  # Low motion
                        # Apply noise reduction
                        enhanced_frame = self._reduce_noise(frame)
                    
                    return enhanced_frame
        
        except Exception:
            pass
        
        return frame
    
    def _enhance_edges(self, frame: np.ndarray) -> np.ndarray:
        """Enhance edges for high-motion scenarios."""
        
        # Apply edge detection
        edges = cv2.Canny(frame, 50, 150)
        
        # Dilate edges
        kernel = np.ones((3,3), np.uint8)
        dilated_edges = cv2.dilate(edges, kernel, iterations=1)
        
        # Combine with original frame
        enhanced = cv2.addWeighted(frame, 0.7, dilated_edges, 0.3, 0)
        
        return enhanced
    
    def _reduce_noise(self, frame: np.ndarray) -> np.ndarray:
        """Reduce noise for low-motion scenarios."""
        
        # Apply bilateral filter for noise reduction while preserving edges
        denoised = cv2.bilateralFilter(frame, 9, 75, 75)
        
        return denoised
