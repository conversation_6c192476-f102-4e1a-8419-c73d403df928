#!/usr/bin/env python3
"""
Phase 2: Enhanced Y-Component Solver with Advanced Optical Flow
Integrates Smart Y-Optimizer with optimized OpenCV optical flow algorithms
"""

import numpy as np
import pandas as pd
import cv2
import time
from typing import Tuple, Optional, Dict, Any
from smart_y_optimizer import SmartYComponentOptimizer
from advanced_optical_flow import HybridOpticalFlow

class Phase2EnhancedSolver(SmartYComponentOptimizer):
    """
    Enhanced solver combining Smart Y-Optimizer with advanced optical flow
    """
    
    def __init__(self):
        """Initialize Phase 2 enhanced solver"""
        super().__init__()
        
        # Initialize advanced optical flow
        self.optical_flow = HybridOpticalFlow()
        
        # Enhanced optical flow parameters
        self.optical_flow_params = {
            'enable_optical_flow': True,
            'optical_flow_weight': 0.2,  # Reduced weight for optical flow in fusion
            'min_optical_flow_confidence': 0.4,  # Higher confidence threshold
            'frame_interval': 0.01,  # Assumed time between frames (10ms)
            'velocity_scaling': 0.5,  # Much smaller pixel-to-velocity scaling factor
            'temporal_smoothing': True,
            'adaptive_weighting': True,
            'max_velocity_magnitude': 100.0,  # Cap maximum velocity
            'outlier_threshold': 3.0  # Standard deviations for outlier removal
        }
        
        # Performance tracking for Phase 2
        self.phase2_stats = {
            'optical_flow_calls': 0,
            'optical_flow_successes': 0,
            'optical_flow_failures': 0,
            'fusion_calls': 0,
            'avg_optical_flow_confidence': 0.0,
            'processing_times': []
        }
        
        # Optical flow history for temporal consistency
        self.optical_flow_history = []
        self.confidence_history = []
    
    def estimate_from_optical_flow_enhanced(self, events_df: pd.DataFrame, 
                                          timestamp: float,
                                          sequence_id: int = None) -> Tuple[float, float, bool]:
        """
        Enhanced optical flow estimation with advanced algorithms
        
        Returns:
            Tuple[float, float, bool]: (y_velocity, confidence, success)
        """
        start_time = time.time()
        self.phase2_stats['optical_flow_calls'] += 1
        
        try:
            # Check if optical flow is enabled
            if not self.optical_flow_params['enable_optical_flow']:
                return 0.0, 0.1, False
            
            # Generate frames from events for optical flow
            frame1, frame2 = self._generate_frames_from_events(events_df, timestamp)
            
            if frame1 is None or frame2 is None:
                self.phase2_stats['optical_flow_failures'] += 1
                return 0.0, 0.1, False
            
            # Calculate hybrid optical flow
            flow_vector, flow_confidence = self.optical_flow.calculate_hybrid_flow(frame1, frame2)
            
            # Extract Y-component velocity
            if flow_vector is not None and len(flow_vector) >= 2:
                # Convert pixel flow to velocity (much more conservative scaling)
                pixel_flow_y = flow_vector[1]
                velocity_y = pixel_flow_y * self.optical_flow_params['velocity_scaling'] / self.optical_flow_params['frame_interval']

                # Apply outlier removal and magnitude limiting
                velocity_magnitude = abs(velocity_y)
                max_allowed = self.optical_flow_params['max_velocity_magnitude']

                if velocity_magnitude > max_allowed:
                    # Scale down extreme velocities
                    velocity_y = velocity_y * (max_allowed / velocity_magnitude)
                    flow_confidence *= 0.3  # Significantly reduce confidence for scaled velocities

                # Apply trajectory-aware scaling
                if hasattr(self, 'current_trajectory_type'):
                    if self.current_trajectory_type == 'ascent':
                        velocity_y *= -1.0  # Ascent has negative Y-velocity
                    elif self.current_trajectory_type == 'descent':
                        velocity_y *= 1.0   # Descent has positive Y-velocity

                # Final sanity check - hard limits
                if abs(velocity_y) > 200:  # Hard limit for lunar lander
                    velocity_y = np.sign(velocity_y) * 20  # Cap at reasonable value
                    flow_confidence = 0.1

                # Apply temporal smoothing if enabled
                if self.optical_flow_params['temporal_smoothing']:
                    velocity_y = self._apply_temporal_smoothing(velocity_y, flow_confidence)

                # Update history
                self.optical_flow_history.append(velocity_y)
                self.confidence_history.append(flow_confidence)
                if len(self.optical_flow_history) > 10:
                    self.optical_flow_history.pop(0)
                    self.confidence_history.pop(0)

                # Check confidence threshold
                if flow_confidence >= self.optical_flow_params['min_optical_flow_confidence']:
                    self.phase2_stats['optical_flow_successes'] += 1
                    success = True
                else:
                    self.phase2_stats['optical_flow_failures'] += 1
                    success = False
                
                # Update performance stats
                self.phase2_stats['avg_optical_flow_confidence'] = np.mean(self.confidence_history)
                
            else:
                velocity_y = 0.0
                flow_confidence = 0.1
                success = False
                self.phase2_stats['optical_flow_failures'] += 1
            
        except Exception as e:
            print(f"Enhanced optical flow estimation failed: {e}")
            velocity_y = 0.0
            flow_confidence = 0.1
            success = False
            self.phase2_stats['optical_flow_failures'] += 1
        
        # Record processing time
        processing_time = time.time() - start_time
        self.phase2_stats['processing_times'].append(processing_time)
        if len(self.phase2_stats['processing_times']) > 100:
            self.phase2_stats['processing_times'].pop(0)
        
        return velocity_y, flow_confidence, success
    
    def _generate_frames_from_events(self, events_df: pd.DataFrame, 
                                   timestamp: float) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Generate frame pairs from event data for optical flow
        """
        try:
            if events_df is None or len(events_df) < 1000:
                return None, None
            
            # Convert timestamps to seconds if needed
            if 't' in events_df.columns:
                timestamps = events_df['t'].values
                if np.max(timestamps) > 1e6:  # Likely in microseconds
                    timestamps = timestamps / 1e6
            else:
                return None, None
            
            # Define time windows for frame generation
            frame_duration = 0.005  # 5ms per frame
            current_time = timestamp
            
            # Time windows for two consecutive frames
            t1_start = current_time - 2 * frame_duration
            t1_end = current_time - frame_duration
            t2_start = current_time - frame_duration
            t2_end = current_time
            
            # Filter events for each frame
            mask1 = (timestamps >= t1_start) & (timestamps < t1_end)
            mask2 = (timestamps >= t2_start) & (timestamps < t2_end)
            
            events1 = events_df[mask1]
            events2 = events_df[mask2]
            
            if len(events1) < 100 or len(events2) < 100:
                return None, None
            
            # Generate frames
            frame1 = self._events_to_frame(events1)
            frame2 = self._events_to_frame(events2)
            
            return frame1, frame2
            
        except Exception as e:
            print(f"Frame generation failed: {e}")
            return None, None
    
    def _events_to_frame(self, events_df: pd.DataFrame, 
                        frame_size: Tuple[int, int] = (640, 480)) -> Optional[np.ndarray]:
        """
        Convert events to frame representation
        """
        try:
            if len(events_df) == 0:
                return None
            
            # Create empty frame
            frame = np.zeros(frame_size[::-1], dtype=np.uint8)  # (height, width)
            
            # Get event coordinates
            x_coords = events_df['x'].values.astype(int)
            y_coords = events_df['y'].values.astype(int)
            
            # Clip coordinates to frame bounds
            x_coords = np.clip(x_coords, 0, frame_size[0] - 1)
            y_coords = np.clip(y_coords, 0, frame_size[1] - 1)
            
            # Set event pixels (accumulate events at same location)
            for x, y in zip(x_coords, y_coords):
                frame[y, x] = min(255, frame[y, x] + 50)  # Accumulate with saturation
            
            # Apply Gaussian blur for smoother optical flow
            frame = cv2.GaussianBlur(frame, (5, 5), 1.0)
            
            return frame
            
        except Exception as e:
            print(f"Events to frame conversion failed: {e}")
            return None
    
    def _apply_temporal_smoothing(self, velocity: float, confidence: float) -> float:
        """
        Apply temporal smoothing to velocity estimates
        """
        if len(self.optical_flow_history) < 2:
            return velocity
        
        # Weighted moving average with confidence weighting
        weights = []
        values = []
        
        # Include current estimate
        weights.append(confidence)
        values.append(velocity)
        
        # Include recent history
        for i, (hist_vel, hist_conf) in enumerate(zip(
            self.optical_flow_history[-3:], self.confidence_history[-3:]
        )):
            # Decay weight for older estimates
            decay_factor = 0.8 ** (len(self.optical_flow_history) - i)
            weights.append(hist_conf * decay_factor)
            values.append(hist_vel)
        
        # Calculate weighted average
        weights = np.array(weights)
        values = np.array(values)
        
        if np.sum(weights) > 0:
            smoothed_velocity = np.sum(values * weights) / np.sum(weights)
        else:
            smoothed_velocity = velocity
        
        return smoothed_velocity
    
    def estimate_y_velocity_enhanced(self, events_df: pd.DataFrame,
                                   timestamp: float,
                                   range_data: np.ndarray = None,
                                   trajectory: np.ndarray = None,
                                   frame1: np.ndarray = None,
                                   frame2: np.ndarray = None,
                                   sequence_id: int = None) -> Tuple[float, float]:
        """
        Enhanced Y-velocity estimation combining all methods including optical flow

        Returns:
            Tuple[float, float]: (y_velocity, confidence)
        """
        self.phase2_stats['fusion_calls'] += 1

        # Create dummy data if not provided
        if range_data is None:
            range_data = np.array([[timestamp, 100.0]])  # Dummy range data
        if trajectory is None:
            trajectory = np.array([[0, 0, 100, 1, 0, 0, 0, 0, 0, 0, 0, 0]])  # Dummy trajectory

        # Get Smart Y-Optimizer estimate (Phase 1 result)
        smart_velocity, smart_confidence = super().estimate_y_velocity(
            events_df, range_data, trajectory, timestamp, frame1, frame2, sequence_id
        )

        # Get enhanced optical flow estimate (Phase 2 addition)
        optical_velocity, optical_confidence, optical_success = self.estimate_from_optical_flow_enhanced(
            events_df, timestamp, sequence_id
        )

        # Adaptive fusion of estimates
        if self.optical_flow_params['adaptive_weighting']:
            final_velocity, final_confidence = self._adaptive_fusion(
                smart_velocity, smart_confidence,
                optical_velocity, optical_confidence, optical_success
            )
        else:
            # Simple weighted fusion
            optical_weight = self.optical_flow_params['optical_flow_weight']
            smart_weight = 1.0 - optical_weight

            if optical_success:
                final_velocity = smart_weight * smart_velocity + optical_weight * optical_velocity
                final_confidence = smart_weight * smart_confidence + optical_weight * optical_confidence
            else:
                final_velocity = smart_velocity
                final_confidence = smart_confidence

        return final_velocity, final_confidence
    
    def _adaptive_fusion(self, smart_velocity: float, smart_confidence: float,
                        optical_velocity: float, optical_confidence: float,
                        optical_success: bool) -> Tuple[float, float]:
        """
        Adaptive fusion of Smart Y-Optimizer and optical flow estimates
        """
        if not optical_success or optical_confidence < self.optical_flow_params['min_optical_flow_confidence']:
            # Use only Smart Y-Optimizer result
            return smart_velocity, smart_confidence
        
        # Calculate adaptive weights based on confidence and trajectory type
        base_optical_weight = self.optical_flow_params['optical_flow_weight']
        
        # Adjust weight based on relative confidence
        confidence_ratio = optical_confidence / max(smart_confidence, 0.1)
        if confidence_ratio > 1.5:
            # Optical flow much more confident
            optical_weight = min(0.5, base_optical_weight * 1.5)
        elif confidence_ratio < 0.5:
            # Smart optimizer much more confident
            optical_weight = max(0.1, base_optical_weight * 0.5)
        else:
            optical_weight = base_optical_weight
        
        smart_weight = 1.0 - optical_weight
        
        # Fuse estimates
        final_velocity = smart_weight * smart_velocity + optical_weight * optical_velocity
        final_confidence = smart_weight * smart_confidence + optical_weight * optical_confidence
        
        return final_velocity, final_confidence
    
    def get_phase2_performance_summary(self) -> Dict[str, Any]:
        """
        Get Phase 2 performance summary
        """
        total_optical_calls = self.phase2_stats['optical_flow_calls']
        success_rate = (self.phase2_stats['optical_flow_successes'] / 
                       max(total_optical_calls, 1))
        
        avg_processing_time = (np.mean(self.phase2_stats['processing_times']) 
                              if self.phase2_stats['processing_times'] else 0.0)
        
        summary = {
            'phase2_optical_flow_calls': total_optical_calls,
            'phase2_success_rate': success_rate,
            'phase2_avg_confidence': self.phase2_stats['avg_optical_flow_confidence'],
            'phase2_fusion_calls': self.phase2_stats['fusion_calls'],
            'phase2_avg_processing_time': avg_processing_time,
            'phase2_optical_flow_history_length': len(self.optical_flow_history),
            'phase2_enabled': self.optical_flow_params['enable_optical_flow']
        }
        
        # Include Phase 1 stats
        phase1_summary = super().get_performance_summary()
        summary.update(phase1_summary)
        
        return summary
    
    def adapt_optical_flow_parameters(self, sequence_characteristics: Dict[str, Any]):
        """
        Adapt optical flow parameters based on sequence characteristics
        """
        if 'event_density' in sequence_characteristics:
            density = sequence_characteristics['event_density']
            
            if density < 5000:  # Low event density
                # Reduce optical flow weight, increase frame duration
                self.optical_flow_params['optical_flow_weight'] = 0.2
                self.optical_flow_params['frame_interval'] = 0.015  # 15ms
                
            elif density > 50000:  # High event density
                # Increase optical flow weight, reduce frame duration
                self.optical_flow_params['optical_flow_weight'] = 0.4
                self.optical_flow_params['frame_interval'] = 0.008  # 8ms
        
        if 'trajectory_type' in sequence_characteristics:
            traj_type = sequence_characteristics['trajectory_type']
            self.current_trajectory_type = traj_type
            
            if traj_type == 'hovering':
                # For hovering, optical flow might be less reliable
                self.optical_flow_params['min_optical_flow_confidence'] = 0.4
                self.optical_flow_params['optical_flow_weight'] = 0.2
                
            elif traj_type in ['ascent', 'descent']:
                # For ascent/descent, optical flow should be more reliable
                self.optical_flow_params['min_optical_flow_confidence'] = 0.3
                self.optical_flow_params['optical_flow_weight'] = 0.3
