#!/usr/bin/env python3
"""
Advanced Optical Flow Algorithms for ELOPE Competition
Combining multiple optical flow methods for improved accuracy
"""

import cv2
import numpy as np
from typing import Tuple, List, Dict, Optional
import pandas as pd
from scipy.optimize import minimize
from sklearn.cluster import DBSCAN

class HybridOpticalFlow:
    """
    Hybrid optical flow combining Farneback dense flow and Lucas-Kanade sparse flow
    """
    
    def __init__(self):
        # Optimized Farneback parameters based on OpenCV documentation
        self.farneback_params = {
            'pyr_scale': 0.5,      # Image scale (<1) to build pyramids
            'levels': 3,           # Number of pyramid levels (3 is optimal for most cases)
            'winsize': 15,         # Averaging window size (15 recommended for lunar surface)
            'iterations': 3,       # Number of iterations at each pyramid level
            'poly_n': 5,           # Size of pixel neighborhood (5 or 7 recommended)
            'poly_sigma': 1.2,     # Standard deviation for Gaussian weighting
            'flags': 0             # Operation flags (0 for default behavior)
        }

        # Optimized Lucas-Kanade parameters based on OpenCV documentation
        self.lk_params = {
            'winSize': (21, 21),   # Window size for tracking (21x21 optimal)
            'maxLevel': 3,         # Maximum pyramid level (3 levels sufficient)
            'criteria': (cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 30, 0.01),
            'flags': 0,            # Operation flags
            'minEigThreshold': 1e-4  # Minimum eigenvalue threshold
        }

        # Optimized feature detection parameters (Shi-Tomasi corners)
        self.feature_params = {
            'maxCorners': 200,     # Maximum number of corners (200 optimal for performance)
            'qualityLevel': 0.01,  # Quality level for corner detection
            'minDistance': 7,      # Minimum distance between corners
            'blockSize': 7,        # Size of averaging block
            'useHarrisDetector': False,  # Use Shi-Tomasi detector
            'k': 0.04              # Harris detector parameter (not used with Shi-Tomasi)
        }

        # Performance tracking
        self.performance_stats = {
            'dense_calls': 0,
            'sparse_calls': 0,
            'successful_tracks': 0,
            'failed_tracks': 0
        }
    
    def calculate_hybrid_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Calculate optical flow using hybrid approach
        
        Args:
            frame1: First frame
            frame2: Second frame
            
        Returns:
            flow_vector: [vx, vy, vz] motion vector
            confidence: Confidence score [0, 1]
        """
        # Dense flow using Farneback
        dense_flow, dense_confidence = self.calculate_dense_flow(frame1, frame2)
        
        # Sparse flow using Lucas-Kanade
        sparse_flow, sparse_confidence = self.calculate_sparse_flow(frame1, frame2)
        
        # Adaptive fusion based on confidence
        if dense_confidence > 0.7 and sparse_confidence > 0.7:
            # Both methods confident - weighted average
            weight_dense = 0.6
            weight_sparse = 0.4
            final_flow = weight_dense * dense_flow + weight_sparse * sparse_flow
            final_confidence = (dense_confidence + sparse_confidence) / 2
        elif dense_confidence > sparse_confidence:
            # Prefer dense flow
            final_flow = 0.8 * dense_flow + 0.2 * sparse_flow
            final_confidence = dense_confidence
        else:
            # Prefer sparse flow
            final_flow = 0.2 * dense_flow + 0.8 * sparse_flow
            final_confidence = sparse_confidence
        
        return final_flow, final_confidence
    
    def calculate_dense_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Calculate dense optical flow using optimized Farneback algorithm
        Based on OpenCV documentation best practices
        """
        self.performance_stats['dense_calls'] += 1

        # Convert to grayscale if needed
        if len(frame1.shape) == 3:
            gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        else:
            gray1, gray2 = frame1.copy(), frame2.copy()

        # Ensure proper data type (uint8 required for Farneback)
        if gray1.dtype != np.uint8:
            gray1 = cv2.convertScaleAbs(gray1)
        if gray2.dtype != np.uint8:
            gray2 = cv2.convertScaleAbs(gray2)

        try:
            # Calculate dense flow using optimized Farneback parameters
            flow = cv2.calcOpticalFlowFarneback(
                gray1, gray2, None,
                self.farneback_params['pyr_scale'],
                self.farneback_params['levels'],
                self.farneback_params['winsize'],
                self.farneback_params['iterations'],
                self.farneback_params['poly_n'],
                self.farneback_params['poly_sigma'],
                self.farneback_params['flags']
            )

            if flow is None:
                return np.array([0.0, 0.0, 0.0]), 0.1

            # Extract flow components
            flow_x = flow[:, :, 0]
            flow_y = flow[:, :, 1]

            # Calculate flow magnitude for quality assessment
            flow_magnitude = np.sqrt(flow_x**2 + flow_y**2)

            # Advanced outlier removal using IQR method (more robust than percentile)
            q75, q25 = np.percentile(flow_magnitude, [75, 25])
            iqr = q75 - q25
            lower_bound = q25 - 1.5 * iqr
            upper_bound = q75 + 1.5 * iqr

            valid_mask = (flow_magnitude >= lower_bound) & (flow_magnitude <= upper_bound)
            valid_mask = valid_mask & (flow_magnitude > 0.5)  # Remove very small motions (noise)

            if np.sum(valid_mask) > 100:  # Sufficient valid pixels
                # Calculate robust statistics
                valid_flow_x = flow_x[valid_mask]
                valid_flow_y = flow_y[valid_mask]
                valid_magnitude = flow_magnitude[valid_mask]

                # Use median for robustness against remaining outliers
                median_flow_x = np.median(valid_flow_x)
                median_flow_y = np.median(valid_flow_y)

                # Calculate confidence based on multiple factors
                # 1. Flow consistency (inverse of standard deviation)
                flow_consistency = 1.0 / (1.0 + np.std(valid_magnitude) / 3.0)

                # 2. Coverage (percentage of valid pixels)
                total_pixels = gray1.shape[0] * gray1.shape[1]
                coverage = np.sum(valid_mask) / total_pixels

                # 3. Magnitude confidence (prefer moderate magnitudes)
                avg_magnitude = np.mean(valid_magnitude)
                magnitude_confidence = np.exp(-abs(avg_magnitude - 2.0) / 5.0)  # Peak at magnitude 2.0

                # Combined confidence score
                confidence = 0.5 * flow_consistency + 0.3 * coverage + 0.2 * magnitude_confidence
                confidence = min(confidence, 0.9)  # Cap maximum confidence

                # Convert pixel flow to motion vector (simplified scaling)
                motion_vector = np.array([median_flow_x, median_flow_y, 0.0])

                self.performance_stats['successful_tracks'] += 1

            else:
                motion_vector = np.array([0.0, 0.0, 0.0])
                confidence = 0.1
                self.performance_stats['failed_tracks'] += 1

        except Exception as e:
            print(f"Dense flow calculation failed: {e}")
            motion_vector = np.array([0.0, 0.0, 0.0])
            confidence = 0.1
            self.performance_stats['failed_tracks'] += 1

        return motion_vector, confidence
    
    def calculate_sparse_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Calculate sparse optical flow using optimized Lucas-Kanade algorithm
        Based on OpenCV documentation best practices
        """
        self.performance_stats['sparse_calls'] += 1

        # Convert to grayscale if needed
        if len(frame1.shape) == 3:
            gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        else:
            gray1, gray2 = frame1.copy(), frame2.copy()

        # Ensure proper data type
        if gray1.dtype != np.uint8:
            gray1 = cv2.convertScaleAbs(gray1)
        if gray2.dtype != np.uint8:
            gray2 = cv2.convertScaleAbs(gray2)

        try:
            # Detect Shi-Tomasi corners in first frame
            corners = cv2.goodFeaturesToTrack(gray1, mask=None, **self.feature_params)

            if corners is None or len(corners) < 10:
                self.performance_stats['failed_tracks'] += 1
                return np.array([0.0, 0.0, 0.0]), 0.1

            # Track features using Lucas-Kanade optical flow
            new_corners, status, tracking_error = cv2.calcOpticalFlowPyrLK(
                gray1, gray2, corners, None, **self.lk_params
            )

            # Filter good tracks based on status and tracking error
            good_mask = (status == 1).flatten()
            if tracking_error is not None:
                # Additional filtering based on tracking error
                error_threshold = np.percentile(tracking_error[good_mask], 75)  # Keep best 75%
                good_mask = good_mask & (tracking_error.flatten() < error_threshold)

            good_old = corners[good_mask]
            good_new = new_corners[good_mask]

            if len(good_old) < 5:
                self.performance_stats['failed_tracks'] += 1
                return np.array([0.0, 0.0, 0.0]), 0.1

            # Calculate motion vectors
            motion_vectors = good_new.reshape(-1, 2) - good_old.reshape(-1, 2)

            # Advanced outlier removal using RANSAC-like approach
            if len(motion_vectors) > 10:
                # Remove outliers using IQR method
                motion_magnitudes = np.linalg.norm(motion_vectors, axis=1)
                q75, q25 = np.percentile(motion_magnitudes, [75, 25])
                iqr = q75 - q25
                lower_bound = q25 - 1.5 * iqr
                upper_bound = q75 + 1.5 * iqr

                inlier_mask = (motion_magnitudes >= lower_bound) & (motion_magnitudes <= upper_bound)
                motion_vectors = motion_vectors[inlier_mask]
                good_old = good_old[inlier_mask]
                good_new = good_new[inlier_mask]

            if len(motion_vectors) < 3:
                self.performance_stats['failed_tracks'] += 1
                return np.array([0.0, 0.0, 0.0]), 0.1

            # Calculate robust motion statistics
            median_motion = np.median(motion_vectors, axis=0)
            motion_std = np.std(motion_vectors, axis=0)

            # Calculate confidence based on multiple factors
            # 1. Tracking success rate
            tracking_ratio = len(good_old) / len(corners)

            # 2. Motion consistency (inverse of standard deviation)
            consistency_x = 1.0 / (1.0 + motion_std[0] / 3.0)
            consistency_y = 1.0 / (1.0 + motion_std[1] / 3.0)
            consistency_score = (consistency_x + consistency_y) / 2.0

            # 3. Feature quality (based on tracking error if available)
            if tracking_error is not None:
                avg_error = np.mean(tracking_error[good_mask])
                error_quality = 1.0 / (1.0 + avg_error / 5.0)
            else:
                error_quality = 0.5

            # Combined confidence score
            confidence = 0.4 * tracking_ratio + 0.4 * consistency_score + 0.2 * error_quality
            confidence = min(confidence, 0.8)  # Cap confidence for sparse flow

            # Estimate depth motion from motion pattern
            vz_estimate = self.estimate_depth_motion(good_old.reshape(-1, 2), motion_vectors)

            # Create 3D motion vector
            motion_vector = np.array([median_motion[0], median_motion[1], vz_estimate])

            self.performance_stats['successful_tracks'] += 1

        except Exception as e:
            print(f"Sparse flow calculation failed: {e}")
            motion_vector = np.array([0.0, 0.0, 0.0])
            confidence = 0.1
            self.performance_stats['failed_tracks'] += 1

        return motion_vector, confidence
    
    def estimate_depth_motion(self, points: np.ndarray, motions: np.ndarray) -> float:
        """
        Estimate depth motion (vz) from 2D motion patterns
        """
        if len(points) < 5:
            return 0.0
        
        # Calculate focus of expansion/contraction
        center_x, center_y = np.mean(points, axis=0)
        
        # Calculate radial motion components
        radial_motions = []
        for i, (point, motion) in enumerate(zip(points, motions)):
            # Vector from center to point
            radial_vec = point - np.array([center_x, center_y])
            radial_length = np.linalg.norm(radial_vec)
            
            if radial_length > 5:  # Avoid division by zero
                # Normalize radial vector
                radial_unit = radial_vec / radial_length
                
                # Project motion onto radial direction
                radial_motion = np.dot(motion, radial_unit)
                radial_motions.append(radial_motion)
        
        if len(radial_motions) > 3:
            # Average radial motion indicates depth change
            avg_radial = np.mean(radial_motions)
            # Scale to reasonable depth velocity
            vz_estimate = avg_radial * 0.1  # Scaling factor
        else:
            vz_estimate = 0.0
        
        return vz_estimate

class MultiScaleOpticalFlow:
    """
    Multi-scale optical flow with enhanced pyramid processing
    """
    
    def __init__(self):
        self.scales = [1.0, 0.75, 0.5, 0.25, 0.125]  # Added finer scale
        self.hybrid_flow = HybridOpticalFlow()
    
    def calculate_multiscale_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Calculate optical flow at multiple scales and combine results
        """
        flows = []
        confidences = []
        weights = []
        
        for scale in self.scales:
            # Resize frames
            h, w = frame1.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            
            if new_h < 50 or new_w < 50:  # Skip too small scales
                continue
            
            scaled_frame1 = cv2.resize(frame1, (new_w, new_h))
            scaled_frame2 = cv2.resize(frame2, (new_w, new_h))
            
            # Calculate flow at this scale
            flow, confidence = self.hybrid_flow.calculate_hybrid_flow(scaled_frame1, scaled_frame2)
            
            # Scale flow back to original resolution
            flow[:2] /= scale  # Scale x, y components
            
            flows.append(flow)
            confidences.append(confidence)
            weights.append(scale)  # Higher weight for larger scales
        
        if not flows:
            return np.array([0.0, 0.0, 0.0]), 0.1
        
        # Weighted combination of flows
        flows = np.array(flows)
        confidences = np.array(confidences)
        weights = np.array(weights)
        
        # Combine weights with confidences
        combined_weights = weights * confidences
        combined_weights /= np.sum(combined_weights)
        
        # Weighted average
        final_flow = np.sum(flows * combined_weights.reshape(-1, 1), axis=0)
        final_confidence = np.mean(confidences)
        
        return final_flow, final_confidence

class PhysicsConstrainedOpticalFlow:
    """
    Optical flow with physics-based constraints for lunar landing
    """
    
    def __init__(self):
        self.multiscale_flow = MultiScaleOpticalFlow()
        self.lunar_gravity = -1.62  # m/s²
        self.max_velocity = 100.0   # m/s reasonable max velocity
        self.velocity_history = []
        
    def calculate_constrained_flow(self, frame1: np.ndarray, frame2: np.ndarray, 
                                 dt: float, altitude: Optional[float] = None) -> Tuple[np.ndarray, float]:
        """
        Calculate optical flow with physics constraints
        """
        # Get raw optical flow
        raw_flow, confidence = self.multiscale_flow.calculate_multiscale_flow(frame1, frame2)
        
        # Apply physics constraints
        constrained_flow = self.apply_physics_constraints(raw_flow, dt, altitude)
        
        # Update velocity history
        self.velocity_history.append(constrained_flow.copy())
        if len(self.velocity_history) > 10:
            self.velocity_history.pop(0)
        
        # Apply temporal smoothing
        smoothed_flow = self.apply_temporal_smoothing(constrained_flow)
        
        return smoothed_flow, confidence
    
    def apply_physics_constraints(self, flow: np.ndarray, dt: float, altitude: Optional[float]) -> np.ndarray:
        """
        Apply physics-based constraints to optical flow
        """
        constrained_flow = flow.copy()
        
        # Velocity magnitude constraint
        velocity_magnitude = np.linalg.norm(constrained_flow)
        if velocity_magnitude > self.max_velocity:
            constrained_flow *= self.max_velocity / velocity_magnitude
        
        # Gravity constraint for Y component
        if len(self.velocity_history) > 0:
            prev_vy = self.velocity_history[-1][1]
            expected_vy = prev_vy + self.lunar_gravity * dt
            
            # Blend with expected value
            alpha = 0.3  # Blending factor
            constrained_flow[1] = alpha * expected_vy + (1 - alpha) * constrained_flow[1]
        
        # Altitude-based Z constraint
        if altitude is not None and altitude < 100:  # Close to surface
            # Limit Z velocity when close to surface
            max_vz = min(10.0, altitude / 10.0)
            constrained_flow[2] = np.clip(constrained_flow[2], -max_vz, max_vz)
        
        return constrained_flow
    
    def apply_temporal_smoothing(self, flow: np.ndarray) -> np.ndarray:
        """
        Apply temporal smoothing using velocity history
        """
        if len(self.velocity_history) < 3:
            return flow
        
        # Simple moving average with recent history
        recent_flows = np.array(self.velocity_history[-3:])
        weights = np.array([0.2, 0.3, 0.5])  # More weight to recent
        
        smoothed_flow = np.sum(recent_flows * weights.reshape(-1, 1), axis=0)
        
        return smoothed_flow
