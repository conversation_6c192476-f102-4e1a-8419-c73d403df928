#!/usr/bin/env python3
"""
Smart Y-Component Optimizer
Intelligent Y-component optimization that handles both ascent and descent trajectories
"""

import numpy as np
import pandas as pd
import cv2
from typing import Tuple, Dict
from scipy.interpolate import interp1d

class SmartYComponentOptimizer:
    """
    Smart Y-component velocity optimizer that intelligently handles ascent/descent
    """
    
    def __init__(self):
        """Initialize smart Y-component optimizer"""
        self.lunar_gravity = -1.62  # m/s² (negative for downward)
        
        # Base calibration factors
        self.base_range_velocity_scale = 1.8
        self.base_optical_flow_scale = 50.0
        self.physics_damping = 0.98
        
        # Trajectory analysis
        self.trajectory_type = None  # 'ascent', 'descent', 'mixed'
        self.velocity_direction = 1.0  # 1.0 for descent, -1.0 for ascent
        self.sequence_characteristics = {}
        self.current_sequence_id = None
        
        # Method performance tracking
        self.method_performance = {
            'range_based': {'total': 0, 'successes': 0, 'avg_error': 0.0},
            'optical_flow': {'total': 0, 'successes': 0, 'avg_error': 0.0},
            'physics_model': {'total': 0, 'successes': 0, 'avg_error': 0.0},
            'interpolation': {'total': 0, 'successes': 0, 'avg_error': 0.0}
        }
        
        # History for temporal consistency
        self.velocity_history = []
        self.confidence_history = []
        self.error_history = []
    
    def analyze_trajectory_type(self, trajectory: np.ndarray, sequence_id: int = None) -> str:
        """
        Analyze trajectory to determine if it's ascent, descent, or mixed
        """
        if trajectory is None or len(trajectory) == 0:
            return 'unknown'
        
        y_velocities = trajectory[:, 4]  # Y velocity component
        
        # Remove outliers for better analysis
        q75, q25 = np.percentile(y_velocities, [75, 25])
        iqr = q75 - q25
        lower_bound = q25 - 1.5 * iqr
        upper_bound = q75 + 1.5 * iqr
        filtered_velocities = y_velocities[(y_velocities >= lower_bound) & (y_velocities <= upper_bound)]
        
        if len(filtered_velocities) == 0:
            return 'unknown'
        
        mean_velocity = np.mean(filtered_velocities)
        velocity_std = np.std(filtered_velocities)
        
        # Determine trajectory type
        if mean_velocity > 20:  # Positive Y velocity = descent
            trajectory_type = 'descent'
            self.velocity_direction = 1.0
        elif mean_velocity < -20:  # Negative Y velocity = ascent
            trajectory_type = 'ascent'
            self.velocity_direction = -1.0
        else:  # Mixed or hovering
            if velocity_std > 30:
                trajectory_type = 'mixed'
                self.velocity_direction = 1.0 if mean_velocity >= 0 else -1.0
            else:
                trajectory_type = 'hovering'
                self.velocity_direction = 1.0
        
        self.trajectory_type = trajectory_type
        
        # Store characteristics
        characteristics = {
            'trajectory_type': trajectory_type,
            'mean_velocity': mean_velocity,
            'velocity_std': velocity_std,
            'velocity_direction': self.velocity_direction,
            'velocity_range': np.max(filtered_velocities) - np.min(filtered_velocities)
        }
        
        if sequence_id is not None:
            self.sequence_characteristics[sequence_id] = characteristics
            self.current_sequence_id = sequence_id
        
        print(f"  🎯 Trajectory analysis: {trajectory_type} (mean: {mean_velocity:.1f} m/s, direction: {self.velocity_direction})")
        
        return trajectory_type
    
    def get_smart_parameters(self, trajectory_type: str, characteristics: Dict) -> Dict:
        """
        Get smart parameters based on trajectory type and characteristics
        """
        params = {
            'range_velocity_scale': self.base_range_velocity_scale,
            'optical_flow_scale': self.base_optical_flow_scale,
            'method_weights': {
                'range_based': 0.4,
                'interpolation': 0.3,
                'optical_flow': 0.2,
                'physics_model': 0.1
            },
            'confidence_threshold': 0.3,
            'velocity_direction': self.velocity_direction
        }
        
        # Adapt based on trajectory type
        if trajectory_type == 'ascent':
            # For ascent trajectories, trust range data more
            params['range_velocity_scale'] = 2.2  # Higher scale for ascent
            params['method_weights']['range_based'] = 0.6
            params['method_weights']['interpolation'] = 0.25
            params['method_weights']['physics_model'] = 0.1
            params['method_weights']['optical_flow'] = 0.05
            params['confidence_threshold'] = 0.4  # Be more conservative
            
        elif trajectory_type == 'descent':
            # For descent trajectories, use balanced approach
            params['range_velocity_scale'] = 1.8
            params['method_weights']['range_based'] = 0.4
            params['method_weights']['interpolation'] = 0.3
            params['method_weights']['optical_flow'] = 0.2
            params['method_weights']['physics_model'] = 0.1
            
        elif trajectory_type == 'mixed':
            # For mixed trajectories, rely more on interpolation
            params['range_velocity_scale'] = 2.0
            params['method_weights']['interpolation'] = 0.5
            params['method_weights']['range_based'] = 0.3
            params['method_weights']['optical_flow'] = 0.15
            params['method_weights']['physics_model'] = 0.05
            params['confidence_threshold'] = 0.5  # Be very conservative
            
        elif trajectory_type == 'hovering':
            # For hovering, use physics model more
            params['range_velocity_scale'] = 1.5
            params['method_weights']['physics_model'] = 0.4
            params['method_weights']['interpolation'] = 0.3
            params['method_weights']['range_based'] = 0.2
            params['method_weights']['optical_flow'] = 0.1
        
        # Adapt based on velocity characteristics
        if 'velocity_std' in characteristics:
            vel_std = characteristics['velocity_std']
            if vel_std > 50:  # High variability
                params['confidence_threshold'] += 0.1
                params['method_weights']['interpolation'] += 0.1
                params['method_weights']['range_based'] -= 0.1
            elif vel_std < 15:  # Low variability
                params['confidence_threshold'] -= 0.1
                params['method_weights']['range_based'] += 0.1
                params['method_weights']['interpolation'] -= 0.1
        
        # Normalize weights
        total_weight = sum(params['method_weights'].values())
        if total_weight > 0:
            for method in params['method_weights']:
                params['method_weights'][method] /= total_weight
        
        return params
    
    def estimate_y_velocity(self, events_df: pd.DataFrame, range_data: np.ndarray, 
                           trajectory: np.ndarray, timestamp: float, 
                           frame1: np.ndarray = None, frame2: np.ndarray = None,
                           sequence_id: int = None) -> Tuple[float, float]:
        """
        Smart Y-component velocity estimation
        """
        # Ensure events_df has time in seconds
        if events_df is not None and not events_df.empty:
            if events_df['t'].max() > 1000:  # Likely in microseconds
                events_df = events_df.copy()
                events_df['t'] = events_df['t'] / 1e6
        
        # Analyze trajectory type if new sequence
        if sequence_id != self.current_sequence_id:
            trajectory_type = self.analyze_trajectory_type(trajectory, sequence_id)
            characteristics = self.sequence_characteristics.get(sequence_id, {})
        else:
            trajectory_type = self.trajectory_type or 'unknown'
            characteristics = self.sequence_characteristics.get(sequence_id, {})
        
        # Get smart parameters
        smart_params = self.get_smart_parameters(trajectory_type, characteristics)
        
        estimations = []
        confidences = []
        methods_used = []
        
        # Method 1: Smart range-based estimation
        if range_data is not None and len(range_data) >= 3:
            range_vy, range_conf = self.estimate_from_range_smart(
                range_data, timestamp, smart_params
            )
            if range_conf > smart_params['confidence_threshold']:
                estimations.append(range_vy)
                confidences.append(range_conf)
                methods_used.append('range_based')
                self._update_method_performance('range_based', True)
            else:
                self._update_method_performance('range_based', False)
        
        # Method 2: Smart optical flow (if available)
        if frame1 is not None and frame2 is not None:
            flow_vy, flow_conf = self.estimate_from_optical_flow_smart(
                frame1, frame2, events_df, smart_params
            )
            if flow_conf > smart_params['confidence_threshold']:
                estimations.append(flow_vy)
                confidences.append(flow_conf)
                methods_used.append('optical_flow')
                self._update_method_performance('optical_flow', True)
            else:
                self._update_method_performance('optical_flow', False)
        
        # Method 3: Smart physics-based prediction
        if len(self.velocity_history) >= 2:
            physics_vy, physics_conf = self.estimate_from_physics_smart(
                timestamp, smart_params
            )
            if physics_conf > smart_params['confidence_threshold']:
                estimations.append(physics_vy)
                confidences.append(physics_conf)
                methods_used.append('physics_model')
                self._update_method_performance('physics_model', True)
            else:
                self._update_method_performance('physics_model', False)
        
        # Method 4: Smart range interpolation
        if range_data is not None and len(range_data) >= 5:
            interp_vy, interp_conf = self.estimate_from_range_interpolation_smart(
                range_data, timestamp, smart_params
            )
            if interp_conf > smart_params['confidence_threshold']:
                estimations.append(interp_vy)
                confidences.append(interp_conf)
                methods_used.append('interpolation')
                self._update_method_performance('interpolation', True)
            else:
                self._update_method_performance('interpolation', False)
        
        # Smart fusion
        if estimations:
            final_vy, final_confidence = self.smart_fusion(
                estimations, confidences, methods_used, smart_params
            )
        else:
            # Smart fallback based on trajectory type
            final_vy, final_confidence = self.smart_fallback(
                trajectory_type, characteristics
            )
        
        # Apply smart constraints
        final_vy = self.apply_smart_constraints(final_vy, trajectory_type, characteristics)
        
        # Update history
        self.velocity_history.append(final_vy)
        self.confidence_history.append(final_confidence)
        if len(self.velocity_history) > 10:
            self.velocity_history.pop(0)
            self.confidence_history.pop(0)
        
        return final_vy, final_confidence
    
    def estimate_from_range_smart(self, range_data: np.ndarray, timestamp: float, 
                                 params: Dict) -> Tuple[float, float]:
        """Smart range-based estimation with trajectory awareness"""
        if len(range_data) < 3:
            return 0.0, 0.1
        
        # Find measurements around timestamp
        time_diffs = np.abs(range_data[:, 0] - timestamp)
        center_idx = np.argmin(time_diffs)
        
        # Adaptive window size based on trajectory type
        if self.trajectory_type == 'mixed':
            window_size = 3  # Larger window for mixed trajectories
        else:
            window_size = 2
        
        start_idx = max(0, center_idx - window_size)
        end_idx = min(len(range_data), center_idx + window_size + 1)
        
        if end_idx - start_idx < 3:
            return 0.0, 0.1
        
        local_data = range_data[start_idx:end_idx]
        times = local_data[:, 0]
        ranges = local_data[:, 1]
        
        try:
            # Use appropriate polynomial degree
            poly_degree = min(2, len(times) - 1)
            coeffs = np.polyfit(times, ranges, poly_degree)
            
            if poly_degree >= 1:
                velocity_at_timestamp = coeffs[-2] if poly_degree == 1 else 2 * coeffs[0] * timestamp + coeffs[1]
                
                # Apply trajectory-aware scaling
                raw_velocity = -velocity_at_timestamp * params['range_velocity_scale']
                
                # Apply direction correction for ascent trajectories
                if self.trajectory_type == 'ascent':
                    # For ascent, range decreases should give negative velocity
                    descent_velocity = raw_velocity * params['velocity_direction']
                else:
                    descent_velocity = raw_velocity
                
                # Calculate confidence
                predicted_ranges = np.polyval(coeffs, times)
                residuals = ranges - predicted_ranges
                rmse = np.sqrt(np.mean(residuals**2))
                confidence = 1.0 / (1.0 + rmse / 3.0)
                
                return descent_velocity, min(confidence, 0.9)
        except:
            pass
        
        # Fallback to simple difference
        if len(times) >= 2:
            dt = times[-1] - times[0]
            dr = ranges[-1] - ranges[0]
            if dt > 0:
                velocity = -dr / dt * params['range_velocity_scale']
                if self.trajectory_type == 'ascent':
                    velocity *= params['velocity_direction']
                return velocity, 0.4
        
        return 0.0, 0.1
    
    def estimate_from_optical_flow_smart(self, frame1: np.ndarray, frame2: np.ndarray, 
                                        events_df: pd.DataFrame, params: Dict) -> Tuple[float, float]:
        """Smart optical flow estimation (simplified for now)"""
        # For now, return low confidence since optical flow is complex
        return 0.0, 0.1
    
    def estimate_from_physics_smart(self, timestamp: float, params: Dict) -> Tuple[float, float]:
        """Smart physics-based estimation with trajectory awareness"""
        if len(self.velocity_history) < 2:
            return 0.0, 0.2
        
        recent_velocities = np.array(self.velocity_history[-3:])
        dt = 0.1
        prev_velocity = self.velocity_history[-1]
        
        # Apply gravity based on trajectory type
        if self.trajectory_type == 'ascent':
            # For ascent, gravity opposes motion
            gravity_effect = self.lunar_gravity * dt * params['velocity_direction']
        else:
            # For descent, gravity assists motion
            gravity_effect = self.lunar_gravity * dt
        
        predicted_velocity = prev_velocity + gravity_effect
        predicted_velocity *= self.physics_damping
        
        # Calculate confidence based on velocity consistency
        if len(recent_velocities) >= 3:
            velocity_std = np.std(recent_velocities)
            consistency = 1.0 / (1.0 + velocity_std / 15.0)
            confidence = min(consistency, 0.7)
        else:
            confidence = 0.4
        
        return predicted_velocity, confidence
    
    def estimate_from_range_interpolation_smart(self, range_data: np.ndarray, 
                                              timestamp: float, params: Dict) -> Tuple[float, float]:
        """Smart range interpolation with trajectory awareness"""
        if len(range_data) < 5:
            return 0.0, 0.1
        
        times = range_data[:, 0]
        ranges = range_data[:, 1]
        
        if timestamp < times[0] or timestamp > times[-1]:
            return 0.0, 0.1
        
        try:
            # Use linear interpolation for better stability
            interp_func = interp1d(times, ranges, kind='linear', fill_value='extrapolate')
            
            dt = 0.01
            range_before = interp_func(timestamp - dt)
            range_after = interp_func(timestamp + dt)
            
            velocity = -(range_after - range_before) / (2 * dt)
            
            # Apply trajectory-aware correction
            if self.trajectory_type == 'ascent':
                velocity *= params['velocity_direction']
            
            # Calculate confidence
            nearby_points = np.sum((times >= timestamp - 1.0) & (times <= timestamp + 1.0))
            confidence = min(nearby_points / 6.0, 0.8)
            
            return velocity, confidence
            
        except Exception:
            return 0.0, 0.1
    
    def smart_fusion(self, estimations: list, confidences: list, 
                    methods: list, params: Dict) -> Tuple[float, float]:
        """Smart fusion with trajectory awareness"""
        if not estimations:
            return 0.0, 0.1
        
        # Weight by both confidence and method performance
        total_weight = 0.0
        weighted_velocity = 0.0
        
        for velocity, confidence, method in zip(estimations, confidences, methods):
            method_weight = params['method_weights'].get(method, 0.1)
            
            # Adjust weight based on method performance
            method_perf = self.method_performance.get(method, {'total': 1, 'successes': 0})
            if method_perf['total'] > 0:
                success_rate = method_perf['successes'] / method_perf['total']
                performance_factor = 0.5 + success_rate  # 0.5 to 1.5 multiplier
            else:
                performance_factor = 1.0
            
            weight = confidence * method_weight * performance_factor
            
            weighted_velocity += weight * velocity
            total_weight += weight
        
        if total_weight > 0:
            final_velocity = weighted_velocity / total_weight
            final_confidence = min(total_weight, 1.0)
        else:
            # Use best single estimate
            best_idx = np.argmax(confidences)
            final_velocity = estimations[best_idx]
            final_confidence = confidences[best_idx]
        
        return final_velocity, final_confidence
    
    def smart_fallback(self, trajectory_type: str, characteristics: Dict) -> Tuple[float, float]:
        """Smart fallback based on trajectory type"""
        if trajectory_type == 'ascent':
            return -80.0, 0.2  # Negative velocity for ascent
        elif trajectory_type == 'descent':
            return 80.0, 0.2   # Positive velocity for descent
        elif trajectory_type == 'mixed':
            return 0.0, 0.1    # Near zero for mixed
        else:
            return 50.0, 0.1   # Conservative default
    
    def apply_smart_constraints(self, velocity: float, trajectory_type: str, 
                               characteristics: Dict) -> float:
        """Apply smart constraints based on trajectory type"""
        if trajectory_type == 'ascent':
            # For ascent, velocity should be negative
            return max(-250.0, min(velocity, 20.0))
        elif trajectory_type == 'descent':
            # For descent, velocity should be positive
            return max(-20.0, min(velocity, 250.0))
        elif trajectory_type == 'mixed':
            # For mixed, allow wider range
            return max(-200.0, min(velocity, 200.0))
        else:
            # Default constraints
            return max(-150.0, min(velocity, 200.0))
    
    def _update_method_performance(self, method: str, success: bool):
        """Update method performance tracking"""
        self.method_performance[method]['total'] += 1
        if success:
            self.method_performance[method]['successes'] += 1
    
    def get_performance_summary(self) -> dict:
        """Get comprehensive performance summary"""
        summary = {
            'trajectory_type': self.trajectory_type,
            'velocity_direction': self.velocity_direction,
            'sequence_characteristics': self.sequence_characteristics,
            'method_performance': self.method_performance,
            'velocity_history_length': len(self.velocity_history),
            'avg_confidence': np.mean(self.confidence_history) if self.confidence_history else 0.0
        }
        
        # Calculate method success rates
        for method, perf in self.method_performance.items():
            if perf['total'] > 0:
                success_rate = perf['successes'] / perf['total']
                summary[f'{method}_success_rate'] = success_rate
            else:
                summary[f'{method}_success_rate'] = 0.0
        
        return summary
