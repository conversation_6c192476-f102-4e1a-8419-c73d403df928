#!/usr/bin/env python3
"""
Calculate Phase 2 Score
Comprehensive evaluation of Phase 2 performance on all training sequences
"""

import numpy as np
import pandas as pd
import json
import os
import time
from typing import Dict, List, Tuple
from phase2_enhanced_solver import Phase2EnhancedSolver
from smart_y_optimizer import SmartYComponentOptimizer

class Phase2ScoreCalculator:
    """Calculate comprehensive Phase 2 score"""
    
    def __init__(self):
        self.phase1_solver = SmartYComponentOptimizer()
        self.phase2_solver = Phase2EnhancedSolver()
        
    def load_sequence_data(self, sequence_id: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load sequence data and ground truth"""
        try:
            data_file = f"../train/{sequence_id:04d}.npz"
            if not os.path.exists(data_file):
                return None, None
            
            data = np.load(data_file)
            
            # Load events
            events = data['events']
            events_df = pd.DataFrame({
                'x': events['x'],
                'y': events['y'], 
                'p': events['p'],
                't': events['t']
            })
            
            # Convert timestamps to seconds if needed
            if np.max(events_df['t']) > 1e6:
                events_df['t'] = events_df['t'] / 1e6
            
            # Load ground truth trajectory
            gt_df = None
            if 'traj' in data and 'timestamps' in data:
                traj = data['traj']
                timestamps = data['timestamps']
                
                gt_df = pd.DataFrame({
                    't': timestamps,
                    'x': traj[:, 0],
                    'y': traj[:, 1],
                    'z': traj[:, 2],
                    'vx': traj[:, 7] if traj.shape[1] > 7 else np.zeros(len(timestamps)),
                    'vy': traj[:, 8] if traj.shape[1] > 8 else np.zeros(len(timestamps)),
                    'vz': traj[:, 9] if traj.shape[1] > 9 else np.zeros(len(timestamps))
                })
            
            return events_df, gt_df
            
        except Exception as e:
            print(f"Error loading sequence {sequence_id}: {e}")
            return None, None
    
    def evaluate_sequence(self, sequence_id: int) -> Dict:
        """Evaluate both phases on a single sequence"""
        print(f"Evaluating sequence {sequence_id:04d}...")
        
        events_df, gt_df = self.load_sequence_data(sequence_id)
        if events_df is None or gt_df is None:
            return {'sequence_id': sequence_id, 'status': 'failed', 'error': 'data_load_failed'}
        
        # Use ground truth timestamps for evaluation
        gt_timestamps = gt_df['t'].values
        gt_velocities = gt_df['vy'].values
        
        # Sample evaluation points (use every 10th point to speed up)
        eval_indices = np.arange(0, len(gt_timestamps), 10)
        eval_timestamps = gt_timestamps[eval_indices]
        eval_gt_velocities = gt_velocities[eval_indices]
        
        print(f"  Events: {len(events_df):,}, GT points: {len(eval_timestamps)}")
        
        phase1_predictions = []
        phase2_predictions = []
        valid_indices = []
        
        for i, timestamp in enumerate(eval_timestamps):
            if i % 50 == 0:
                print(f"    Progress: {i}/{len(eval_timestamps)}")
            
            # Create event window
            window_size = 0.05  # 50ms window
            event_mask = (events_df['t'] >= timestamp - window_size) & (events_df['t'] <= timestamp + window_size)
            window_events = events_df[event_mask]
            
            if len(window_events) < 50:  # Need minimum events
                continue
            
            # Create dummy range and trajectory data
            range_data = np.array([[timestamp, 100.0]])
            trajectory = np.array([[0, 0, 100, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
            
            try:
                # Phase 1 prediction
                phase1_vel, phase1_conf = self.phase1_solver.estimate_y_velocity(
                    window_events, range_data, trajectory, timestamp
                )
                
                # Phase 2 prediction
                phase2_vel, phase2_conf = self.phase2_solver.estimate_y_velocity_enhanced(
                    window_events, timestamp
                )
                
                phase1_predictions.append(phase1_vel)
                phase2_predictions.append(phase2_vel)
                valid_indices.append(i)
                
            except Exception as e:
                continue
        
        if len(phase1_predictions) == 0:
            return {'sequence_id': sequence_id, 'status': 'failed', 'error': 'no_predictions'}
        
        # Calculate ELOPE competition score (normalized RMSE)
        valid_gt_vy = eval_gt_velocities[valid_indices]
        valid_gt_z = gt_df['z'].values[eval_indices][valid_indices]  # Heights for normalization

        # Calculate velocity errors
        phase1_errors = np.array(phase1_predictions) - valid_gt_vy
        phase2_errors = np.array(phase2_predictions) - valid_gt_vy

        # ELOPE scoring: normalize by altitude (z coordinate)
        # Score = mean(sqrt(velocity_error^2) / altitude)
        phase1_normalized_errors = np.abs(phase1_errors) / np.abs(valid_gt_z)
        phase2_normalized_errors = np.abs(phase2_errors) / np.abs(valid_gt_z)

        phase1_rmse = np.mean(phase1_normalized_errors)
        phase2_rmse = np.mean(phase2_normalized_errors)
        
        improvement = ((phase1_rmse - phase2_rmse) / phase1_rmse) * 100
        
        result = {
            'sequence_id': sequence_id,
            'status': 'success',
            'valid_predictions': len(phase1_predictions),
            'phase1_rmse': phase1_rmse,
            'phase2_rmse': phase2_rmse,
            'improvement_percent': improvement,
            'phase1_mean_velocity': np.mean(phase1_predictions),
            'phase2_mean_velocity': np.mean(phase2_predictions),
            'gt_mean_velocity': np.mean(valid_gt_vy),
            'phase1_std': np.std(phase1_predictions),
            'phase2_std': np.std(phase2_predictions),
            'gt_std': np.std(valid_gt_vy)
        }
        
        print(f"    Phase 1 RMSE: {phase1_rmse:.2f}")
        print(f"    Phase 2 RMSE: {phase2_rmse:.2f}")
        print(f"    Improvement: {improvement:.1f}%")
        
        return result
    
    def calculate_overall_score(self, sequences: List[int] = None) -> Dict:
        """Calculate overall score across all sequences"""
        if sequences is None:
            # Use all available training sequences (0-27)
            sequences = list(range(28))
        
        print(f"=== Calculating Phase 2 Score on {len(sequences)} sequences ===")
        
        all_results = []
        successful_results = []
        
        for seq_id in sequences:
            try:
                result = self.evaluate_sequence(seq_id)
                all_results.append(result)
                
                if result['status'] == 'success':
                    successful_results.append(result)
                    
            except Exception as e:
                print(f"Error evaluating sequence {seq_id}: {e}")
                all_results.append({
                    'sequence_id': seq_id, 
                    'status': 'error', 
                    'error': str(e)
                })
        
        if len(successful_results) == 0:
            print("No successful evaluations!")
            return {'status': 'failed', 'results': all_results}
        
        # Calculate overall statistics
        phase1_rmses = [r['phase1_rmse'] for r in successful_results]
        phase2_rmses = [r['phase2_rmse'] for r in successful_results]
        improvements = [r['improvement_percent'] for r in successful_results]
        
        # Overall RMSE (weighted by number of predictions)
        total_phase1_sse = sum(r['phase1_rmse']**2 * r['valid_predictions'] for r in successful_results)
        total_phase2_sse = sum(r['phase2_rmse']**2 * r['valid_predictions'] for r in successful_results)
        total_predictions = sum(r['valid_predictions'] for r in successful_results)
        
        overall_phase1_rmse = np.sqrt(total_phase1_sse / total_predictions)
        overall_phase2_rmse = np.sqrt(total_phase2_sse / total_predictions)
        overall_improvement = ((overall_phase1_rmse - overall_phase2_rmse) / overall_phase1_rmse) * 100
        
        summary = {
            'status': 'success',
            'total_sequences': len(sequences),
            'successful_sequences': len(successful_results),
            'total_predictions': total_predictions,
            
            # Overall scores
            'overall_phase1_rmse': overall_phase1_rmse,
            'overall_phase2_rmse': overall_phase2_rmse,
            'overall_improvement_percent': overall_improvement,
            
            # Statistics
            'mean_phase1_rmse': np.mean(phase1_rmses),
            'mean_phase2_rmse': np.mean(phase2_rmses),
            'std_phase1_rmse': np.std(phase1_rmses),
            'std_phase2_rmse': np.std(phase2_rmses),
            'mean_improvement_percent': np.mean(improvements),
            'std_improvement_percent': np.std(improvements),
            
            # Best and worst improvements
            'best_improvement_percent': np.max(improvements),
            'worst_improvement_percent': np.min(improvements),
            'sequences_with_improvement': sum(1 for imp in improvements if imp > 0),
            'improvement_success_rate': sum(1 for imp in improvements if imp > 0) / len(improvements),
            
            # Detailed results
            'individual_results': all_results
        }
        
        # Print summary
        print(f"\n=== Phase 2 Score Summary ===")
        print(f"Successful sequences: {len(successful_results)}/{len(sequences)}")
        print(f"Total predictions: {total_predictions:,}")
        print(f"")
        print(f"Overall Phase 1 RMSE: {overall_phase1_rmse:.4f}")
        print(f"Overall Phase 2 RMSE: {overall_phase2_rmse:.4f}")
        print(f"Overall Improvement: {overall_improvement:.2f}%")
        print(f"")
        print(f"Mean improvement per sequence: {np.mean(improvements):.2f}% ± {np.std(improvements):.2f}%")
        print(f"Best sequence improvement: {np.max(improvements):.2f}%")
        print(f"Worst sequence improvement: {np.min(improvements):.2f}%")
        print(f"Sequences with improvement: {sum(1 for imp in improvements if imp > 0)}/{len(improvements)} ({sum(1 for imp in improvements if imp > 0)/len(improvements)*100:.1f}%)")
        
        # Compare to baseline (competition scores)
        baseline_score = 0.1383  # Current competition score (9th place)
        target_score = 0.06     # Target for top 5
        current_score = overall_phase2_rmse
        improvement_from_current = ((baseline_score - current_score) / baseline_score) * 100
        improvement_needed_for_target = ((current_score - target_score) / current_score) * 100
        
        print(f"")
        print(f"=== Competition Score Analysis ===")
        print(f"Current competition score (9th place): {baseline_score:.4f}")
        print(f"Phase 2 score: {current_score:.4f}")
        print(f"Improvement from current: {improvement_from_current:.2f}%")
        print(f"Target score (top 5): {target_score:.4f}")
        if current_score > target_score:
            print(f"Additional improvement needed: {improvement_needed_for_target:.2f}%")
        else:
            print(f"✓ Target achieved! Exceeded by {-improvement_needed_for_target:.2f}%")

        summary['baseline_score'] = baseline_score
        summary['target_score'] = target_score
        summary['improvement_from_current'] = improvement_from_current
        summary['improvement_needed_for_target'] = improvement_needed_for_target
        
        return summary
    
    def save_results(self, results: Dict, filename: str = "phase2_score_results.json"):
        """Save results to file"""
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"Results saved to: {filename}")

def main():
    """Main evaluation function"""
    calculator = Phase2ScoreCalculator()
    
    # Test on first 10 sequences for quick evaluation
    print("Quick evaluation on first 10 sequences...")
    quick_results = calculator.calculate_overall_score(list(range(10)))
    calculator.save_results(quick_results, "phase2_quick_score.json")
    
    # If quick test looks good, run full evaluation
    if quick_results['status'] == 'success' and quick_results['successful_sequences'] >= 5:
        print(f"\n{'='*60}")
        print("Running full evaluation on all 28 sequences...")
        full_results = calculator.calculate_overall_score()
        calculator.save_results(full_results, "phase2_full_score.json")
        return full_results
    else:
        print("Quick test failed or insufficient successful sequences")
        return quick_results

if __name__ == "__main__":
    results = main()
