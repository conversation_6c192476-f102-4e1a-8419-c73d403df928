#!/usr/bin/env python3
"""
Quick Phase 2 Test: Verify fixes to optical flow scaling
"""

import numpy as np
import pandas as pd
import time
from phase2_enhanced_solver import Phase2EnhancedSolver
from smart_y_optimizer import SmartYComponentOptimizer

def test_single_sequence(sequence_id: int = 0, num_tests: int = 10):
    """Quick test on a single sequence"""
    print(f"=== Quick Phase 2 Test - Sequence {sequence_id:04d} ===")
    
    try:
        # Load data
        data_file = f"../train/{sequence_id:04d}.npz"
        data = np.load(data_file)
        
        events = data['events']
        events_df = pd.DataFrame({
            'x': events['x'],
            'y': events['y'],
            'p': events['p'],
            't': events['t']
        })
        
        # Convert timestamps to seconds if needed
        if np.max(events_df['t']) > 1e6:
            events_df['t'] = events_df['t'] / 1e6
        
        print(f"Loaded {len(events_df):,} events")
        print(f"Time range: {events_df['t'].min():.2f} - {events_df['t'].max():.2f} seconds")
        
        # Create solvers
        phase1_solver = SmartYComponentOptimizer()
        phase2_solver = Phase2EnhancedSolver()
        
        # Test timestamps
        time_span = events_df['t'].max() - events_df['t'].min()
        test_timestamps = np.linspace(
            events_df['t'].min() + time_span * 0.2,
            events_df['t'].max() - time_span * 0.2,
            num_tests
        )
        
        print(f"\nTesting {num_tests} time points...")
        
        phase1_velocities = []
        phase2_velocities = []
        optical_successes = 0
        
        for i, timestamp in enumerate(test_timestamps):
            # Create event window
            window_size = 0.05  # 50ms
            event_mask = (events_df['t'] >= timestamp - window_size) & (events_df['t'] <= timestamp + window_size)
            window_events = events_df[event_mask]
            
            if len(window_events) < 100:
                continue
            
            # Dummy data
            range_data = np.array([[timestamp, 100.0]])
            trajectory = np.array([[0, 0, 100, 1, 0, 0, 0, 0, 0, 0, 0, 0]])
            
            # Test Phase 1
            try:
                phase1_vel, phase1_conf = phase1_solver.estimate_y_velocity(
                    window_events, range_data, trajectory, timestamp
                )
                phase1_velocities.append(phase1_vel)
            except Exception as e:
                print(f"Phase 1 failed at {timestamp:.2f}: {e}")
                continue
            
            # Test Phase 2
            try:
                phase2_vel, phase2_conf = phase2_solver.estimate_y_velocity_enhanced(
                    window_events, timestamp
                )
                phase2_velocities.append(phase2_vel)
                
                # Check optical flow
                optical_vel, optical_conf, optical_success = phase2_solver.estimate_from_optical_flow_enhanced(
                    window_events, timestamp
                )
                if optical_success:
                    optical_successes += 1
                
                print(f"  {i+1:2d}: P1={phase1_vel:6.2f}, P2={phase2_vel:6.2f}, OF={optical_vel:6.2f} (conf={optical_conf:.2f}, success={optical_success})")
                
            except Exception as e:
                print(f"Phase 2 failed at {timestamp:.2f}: {e}")
                phase2_velocities.append(phase1_velocities[-1])
        
        if len(phase1_velocities) > 0:
            print(f"\n=== Results Summary ===")
            print(f"Valid test points: {len(phase1_velocities)}")
            print(f"Phase 1 velocity: {np.mean(phase1_velocities):6.2f} ± {np.std(phase1_velocities):5.2f}")
            print(f"Phase 2 velocity: {np.mean(phase2_velocities):6.2f} ± {np.std(phase2_velocities):5.2f}")
            print(f"Velocity difference: {np.mean(phase2_velocities) - np.mean(phase1_velocities):6.2f}")
            print(f"Optical flow success rate: {optical_successes}/{len(phase2_velocities)} ({optical_successes/len(phase2_velocities)*100:.1f}%)")
            
            # Check for reasonable velocity ranges
            max_p1 = max(abs(v) for v in phase1_velocities)
            max_p2 = max(abs(v) for v in phase2_velocities)
            print(f"Max velocity magnitude - P1: {max_p1:.2f}, P2: {max_p2:.2f}")
            
            if max_p2 < 500:  # Reasonable range
                print("✓ Phase 2 velocities are in reasonable range")
            else:
                print("⚠ Phase 2 velocities may still be too large")
            
            # Performance summary
            phase2_perf = phase2_solver.get_phase2_performance_summary()
            print(f"\nPhase 2 Performance:")
            print(f"  Optical flow calls: {phase2_perf.get('phase2_optical_flow_calls', 0)}")
            print(f"  Success rate: {phase2_perf.get('phase2_success_rate', 0):.1%}")
            print(f"  Avg confidence: {phase2_perf.get('phase2_avg_confidence', 0):.3f}")
            
        else:
            print("No valid test results")
            
    except Exception as e:
        print(f"Test failed: {e}")

def test_multiple_sequences():
    """Test multiple sequences quickly"""
    sequences = [0, 1, 2, 4, 5]  # Mix of high and low event density
    
    print("=== Quick Multi-Sequence Test ===")
    
    for seq_id in sequences:
        try:
            test_single_sequence(seq_id, num_tests=5)
            print()
        except Exception as e:
            print(f"Sequence {seq_id} failed: {e}\n")

if __name__ == "__main__":
    # Test single sequence first
    test_single_sequence(0, num_tests=10)
    
    print("\n" + "="*60 + "\n")
    
    # Test multiple sequences
    test_multiple_sequences()
